# GraphQL schema location
schema:
  - graph/schema.graphqls

# Where should the generated server code go?
exec:
  filename: graph/generated/generated.go
  package: generated

# Where should any generated models go?
model:
  filename: graph/model/models_gen.go
  package: model

# Where should the resolver implementations go?
resolver:
  layout: follow-schema
  dir: graph
  package: graph
  filename_template: "{name}.resolvers.go"

# Optional: turn on/off introspection. When disabled, clients will not be able to query the server for the GraphQL schema.
# introspection: true

# Optional: turn on/off the playground. When disabled, the playground will not be available.
# playground: true

# gqlgen will search for any type names in the schema in these go packages
# if they match it will use them, otherwise it will generate them.
autobind:
  - "crypto-bubble-map-be/internal/domain/entity"

# This section declares type mapping between the GraphQL and Go type systems
models:
  # Scalars
  Time:
    model:
      - time.Time
  JSON:
    model:
      - map[string]interface{}
  BigInt:
    model:
      - string

# Optional: set to speed up generation time by not performing a final validation pass.
skip_validation: false

# Optional: set to skip running `go mod tidy` when generating server code
skip_mod_tidy: false
