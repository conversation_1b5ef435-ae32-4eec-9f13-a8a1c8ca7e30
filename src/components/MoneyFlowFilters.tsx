import React, { useState, useEffect } from 'react';
import { 
  <PERSON>aS<PERSON>ch, FaFilter, FaCalendarAlt, FaCoins, FaChevronDown, 
  FaExclamationTriangle, FaDollarSign, FaClock, FaHashtag 
} from 'react-icons/fa';
import { MoneyFlowFilters as IMoneyFlowFilters } from '@/services/moneyFlowService';

interface MoneyFlowFiltersProps {
  filters: IMoneyFlowFilters;
  onFiltersChange: (filters: IMoneyFlowFilters) => void;
  availableTokens?: string[];
  className?: string;
}

const MoneyFlowFilters: React.FC<MoneyFlowFiltersProps> = ({
  filters,
  onFiltersChange,
  availableTokens = ['ETH', 'USDC', 'USDT', 'WETH', 'DAI', 'LINK', 'UNI'],
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState<IMoneyFlowFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof IMoneyFlowFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleTimeRangeChange = (field: 'start' | 'end', value: string) => {
    const newTimeRange = {
      ...localFilters.timeRange,
      [field]: value
    };
    handleFilterChange('timeRange', newTimeRange);
  };

  const handleBlockRangeChange = (field: 'start' | 'end', value: string) => {
    const numValue = parseInt(value) || 0;
    const newBlockRange = {
      ...localFilters.blockRange,
      [field]: numValue
    };
    handleFilterChange('blockRange', newBlockRange);
  };

  const resetFilters = () => {
    const defaultFilters: IMoneyFlowFilters = {
      flowType: 'both',
      transferType: 'both',
      topN: 50,
      riskLevel: 'all'
    };
    setLocalFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const getPresetTimeRanges = () => [
    { label: '24 Hours', value: { start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0], end: new Date().toISOString().split('T')[0] } },
    { label: '7 Days', value: { start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], end: new Date().toISOString().split('T')[0] } },
    { label: '30 Days', value: { start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], end: new Date().toISOString().split('T')[0] } },
    { label: '90 Days', value: { start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], end: new Date().toISOString().split('T')[0] } }
  ];

  return (
    <div className={`bg-background-secondary rounded-lg border border-border ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-blue-500">
            <FaFilter className="text-sm text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground">Advanced Filters</h3>
            <p className="text-xs text-foreground-muted">Customize your money flow analysis</p>
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-foreground-muted hover:text-foreground transition-colors"
        >
          <span>{isExpanded ? 'Less' : 'More'}</span>
          <FaChevronDown className={`transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
        </button>
      </div>

      {/* Basic Filters */}
      <div className="p-4 space-y-4">
        {/* Flow Type and Transfer Type */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Flow Direction</label>
            <select
              value={localFilters.flowType}
              onChange={(e) => handleFilterChange('flowType', e.target.value)}
              className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
            >
              <option value="both">Both Directions</option>
              <option value="inbound">Inbound Only</option>
              <option value="outbound">Outbound Only</option>
            </select>
          </div>

          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Transfer Type</label>
            <select
              value={localFilters.transferType}
              onChange={(e) => handleFilterChange('transferType', e.target.value)}
              className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
            >
              <option value="both">ETH & Tokens</option>
              <option value="eth">ETH Only</option>
              <option value="token">Tokens Only</option>
            </select>
          </div>
        </div>

        {/* Top N and Search */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Top Accounts</label>
            <select
              value={localFilters.topN}
              onChange={(e) => handleFilterChange('topN', parseInt(e.target.value))}
              className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
            >
              <option value={10}>Top 10</option>
              <option value={20}>Top 20</option>
              <option value={50}>Top 50</option>
              <option value={100}>Top 100</option>
            </select>
          </div>

          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Search Account</label>
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted text-xs" />
              <input
                type="text"
                placeholder="Address or label..."
                value={localFilters.searchQuery || ''}
                onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                className="w-full pl-9 pr-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="px-4 pb-4 space-y-4 border-t border-border pt-4">
          {/* Token Filter */}
          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Specific Token</label>
            <select
              value={localFilters.tokenFilter || 'all'}
              onChange={(e) => handleFilterChange('tokenFilter', e.target.value === 'all' ? undefined : e.target.value)}
              className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
            >
              <option value="all">All Tokens</option>
              {availableTokens.map(token => (
                <option key={token} value={token}>{token}</option>
              ))}
            </select>
          </div>

          {/* Time Range */}
          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Time Range</label>
            <div className="space-y-2">
              {/* Preset buttons */}
              <div className="flex flex-wrap gap-2">
                {getPresetTimeRanges().map((preset, index) => (
                  <button
                    key={index}
                    onClick={() => handleFilterChange('timeRange', preset.value)}
                    className="px-3 py-1 text-xs font-medium bg-background-tertiary hover:bg-accent-400/20 text-foreground-muted hover:text-foreground rounded-lg transition-colors"
                  >
                    {preset.label}
                  </button>
                ))}
              </div>
              
              {/* Custom date inputs */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-foreground-muted mb-1">From</label>
                  <input
                    type="date"
                    value={localFilters.timeRange?.start?.split('T')[0] || ''}
                    onChange={(e) => handleTimeRangeChange('start', e.target.value)}
                    className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-xs text-foreground-muted mb-1">To</label>
                  <input
                    type="date"
                    value={localFilters.timeRange?.end?.split('T')[0] || ''}
                    onChange={(e) => handleTimeRangeChange('end', e.target.value)}
                    className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Block Range */}
          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Block Range</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-foreground-muted mb-1">From Block</label>
                <input
                  type="number"
                  placeholder="18000000"
                  value={localFilters.blockRange?.start || ''}
                  onChange={(e) => handleBlockRangeChange('start', e.target.value)}
                  className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-xs text-foreground-muted mb-1">To Block</label>
                <input
                  type="number"
                  placeholder="18100000"
                  value={localFilters.blockRange?.end || ''}
                  onChange={(e) => handleBlockRangeChange('end', e.target.value)}
                  className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Value Range */}
          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Value Range (USD)</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-foreground-muted mb-1">Min Value</label>
                <input
                  type="number"
                  placeholder="0"
                  value={localFilters.minValue || ''}
                  onChange={(e) => handleFilterChange('minValue', parseFloat(e.target.value) || undefined)}
                  className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-xs text-foreground-muted mb-1">Max Value</label>
                <input
                  type="number"
                  placeholder="1000000"
                  value={localFilters.maxValue || ''}
                  onChange={(e) => handleFilterChange('maxValue', parseFloat(e.target.value) || undefined)}
                  className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Risk Level */}
          <div>
            <label className="block text-xs font-medium text-foreground-muted mb-2">Risk Level</label>
            <select
              value={localFilters.riskLevel || 'all'}
              onChange={(e) => handleFilterChange('riskLevel', e.target.value)}
              className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
            >
              <option value="all">All Risk Levels</option>
              <option value="low">Low Risk</option>
              <option value="medium">Medium Risk</option>
              <option value="high">High Risk</option>
            </select>
          </div>

          {/* Reset Button */}
          <div className="pt-2">
            <button
              onClick={resetFilters}
              className="w-full px-4 py-2 text-sm font-medium text-foreground-muted hover:text-foreground bg-background-tertiary hover:bg-accent-400/20 rounded-lg transition-colors"
            >
              Reset All Filters
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MoneyFlowFilters;
