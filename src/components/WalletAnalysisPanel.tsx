import React, { useState, useEffect } from 'react';
import {
  FaWallet, FaShieldAlt, FaExclamationTriangle, FaChartLine,
  FaExchangeAlt, FaRobot, FaFlag, FaEye, FaClock, FaGlobe,
  FaCoins, FaNetworkWired, FaUserSecret, FaBan, FaTimes, FaCheck, FaStar, FaRegStar
} from 'react-icons/fa';
import { Node } from '@/services/neo4jService';
import { RiskScore } from '@/services/riskScoringService';
import Tooltip from './Tooltip';
import { useLayout } from './LayoutManager';
import { getWatchListService } from '@/services/watchListService';

interface WalletAnalysisPanelProps {
  selectedWallet: Node | null;
  onClose: () => void;
  embedded?: boolean; // When true, don't use LayoutManager
  onAddToWatchList?: (address: string) => void; // Callback when wallet is added to watch list
}

interface WalletMetrics {
  balance: number;
  tokenCount: number;
  transactionCount: number;
  firstSeen: string;
  lastActivity: string;
  walletAge: number;
  avgTransactionValue: number;
  uniqueCounterparties: number;
}

const WalletAnalysisPanel: React.FC<WalletAnalysisPanelProps> = ({
  selectedWallet,
  onClose,
  embedded = false,
  onAddToWatchList
}) => {
  // Only use layout when not embedded
  const layoutContext = embedded ? null : useLayout();
  const layout = layoutContext?.layout || { isMobile: false, isTablet: false, isDesktop: true };
  const getComponentPosition = layoutContext?.getComponentPosition || (() => ({
    position: 'relative',
    zIndex: 1,
    width: '100%',
    maxWidth: '100%',
    maxHeight: '100%',
    overflow: 'auto'
  }));
  const [metrics, setMetrics] = useState<WalletMetrics | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'risk' | 'activity' | 'connections'>('overview');
  const [isInWatchList, setIsInWatchList] = useState(false);
  const [isAddingToWatchList, setIsAddingToWatchList] = useState(false);
  const [isRemovingFromWatchList, setIsRemovingFromWatchList] = useState(false);

  // Get risk score from the selected wallet (passed from parent)
  const riskScore = (selectedWallet as any)?.riskScore as RiskScore | undefined;

  useEffect(() => {
    if (selectedWallet) {
      fetchWalletAnalysis();
      checkWatchListStatus();
    }
  }, [selectedWallet]);

  // Check if wallet is already in watch list
  const checkWatchListStatus = () => {
    if (!selectedWallet || typeof window === 'undefined') return;

    try {
      const service = getWatchListService();
      setIsInWatchList(service.isWatched(selectedWallet.address));
    } catch (error) {
      console.error('Failed to check watch list status:', error);
    }
  };

  const fetchWalletAnalysis = async () => {
    setLoading(true);
    try {
      // Simulate API calls for wallet analysis
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock wallet metrics (risk score comes from parent)
      setMetrics({
        balance: parseFloat(selectedWallet?.balance || '0') || Math.random() * 1000,
        tokenCount: Math.floor(Math.random() * 50),
        transactionCount: selectedWallet?.transactionCount || Math.floor(Math.random() * 10000),
        firstSeen: '2023-01-15',
        lastActivity: '2024-12-15',
        walletAge: 365,
        avgTransactionValue: Math.random() * 100,
        uniqueCounterparties: Math.floor(Math.random() * 500)
      });
    } catch (error) {
      console.error('Error fetching wallet analysis:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-400 bg-red-500/20';
    if (score >= 60) return 'text-orange-400 bg-orange-500/20';
    if (score >= 40) return 'text-yellow-400 bg-yellow-500/20';
    return 'text-green-400 bg-green-500/20';
  };

  const getRiskLevel = (score: number) => {
    if (score >= 80) return 'HIGH RISK';
    if (score >= 60) return 'MEDIUM RISK';
    if (score >= 40) return 'LOW RISK';
    return 'SAFE';
  };

  // Handle adding wallet to watch list
  const handleAddToWatchList = async () => {
    if (!selectedWallet || isAddingToWatchList || typeof window === 'undefined') return;

    setIsAddingToWatchList(true);
    try {
      const service = getWatchListService();

      // Determine tags based on wallet characteristics
      const tags: string[] = [];
      if (selectedWallet.tags) {
        tags.push(...selectedWallet.tags);
      }

      // Add risk-based tags
      const riskScore = (selectedWallet as any)?.riskScore?.totalScore || 0;
      if (riskScore >= 70) {
        tags.push('High-Risk');
      } else if (riskScore >= 40) {
        tags.push('Medium-Risk');
      }

      // Add balance-based tags
      const balance = parseFloat(selectedWallet.balance || '0');
      if (balance >= 1000) {
        tags.push('Whale');
      }

      service.addWallet({
        address: selectedWallet.address,
        label: selectedWallet.label || `Wallet ${selectedWallet.address.slice(0, 8)}...`,
        tags: Array.from(new Set(tags)), // Remove duplicates
        balance: selectedWallet.balance,
        transactionCount: selectedWallet.transactionCount,
        riskScore: riskScore,
        alertsEnabled: true,
        customThresholds: {
          balanceChange: 10, // 10% change
          transactionVolume: 100, // 100 ETH
          riskScoreIncrease: 20 // 20 points
        }
      });

      setIsInWatchList(true);

      // Notify parent component
      onAddToWatchList?.(selectedWallet.address);

      console.log('Wallet added to watch list:', selectedWallet.address);
    } catch (error) {
      console.error('Failed to add wallet to watch list:', error);
      // Could show error message to user
    } finally {
      setIsAddingToWatchList(false);
    }
  };

  // Handle removing wallet from watch list
  const handleRemoveFromWatchList = async () => {
    if (!selectedWallet || isRemovingFromWatchList || typeof window === 'undefined') return;

    setIsRemovingFromWatchList(true);
    try {
      const service = getWatchListService();
      const watchedWallet = service.getWalletByAddress(selectedWallet.address);

      if (watchedWallet) {
        service.removeWallet(watchedWallet.id);
        setIsInWatchList(false);
        console.log('Wallet removed from watch list:', selectedWallet.address);
      }
    } catch (error) {
      console.error('Failed to remove wallet from watch list:', error);
      // Could show error message to user
    } finally {
      setIsRemovingFromWatchList(false);
    }
  };

  // Render tab content function (shared between mobile and desktop)
  const renderTabContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-32">
          <div className="w-8 h-8 rounded-full border-2 animate-spin border-accent-400 border-t-transparent"></div>
        </div>
      );
    }

    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-4">
            {/* Risk Summary */}
            {riskScore && (
              <div className="p-3 rounded-lg glass-card">
                <h4 className="flex gap-2 items-center mb-2 text-sm font-semibold">
                  <FaShieldAlt className="text-accent-400" />
                  Risk Assessment
                </h4>
                <div className="flex justify-between items-center">
                  <div className="flex gap-2 items-center">
                    <div className={`w-3 h-3 rounded-full ${
                      riskScore.riskLevel === 'critical' ? 'bg-red-500' :
                      riskScore.riskLevel === 'high' ? 'bg-orange-500' :
                      riskScore.riskLevel === 'medium' ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}></div>
                    <span className="text-sm font-medium capitalize">{riskScore.riskLevel} Risk</span>
                  </div>
                  <span className="text-sm font-bold">{riskScore.totalScore}/100</span>
                </div>
                {riskScore.flags.length > 0 && (
                  <div className="mt-2 text-xs text-red-400">
                    {riskScore.flags.length} flag(s) detected
                  </div>
                )}
              </div>
            )}

            {/* Wallet Classification */}
            <div className="p-3 rounded-lg glass-card">
              <h4 className="flex gap-2 items-center mb-2 text-sm font-semibold">
                <FaWallet className="text-accent-400" />
                Wallet Classification
              </h4>
              <div className="flex flex-wrap gap-2">
                {selectedWallet?.tags?.map(tag => (
                  <span key={tag} className="px-2 py-1 text-xs rounded-full bg-accent-500/20 text-accent-400">
                    {tag}
                  </span>
                ))}
                {(!selectedWallet?.tags || selectedWallet.tags.length === 0) && (
                  <span className="text-xs text-foreground-muted">No classification tags</span>
                )}
              </div>
            </div>

            {/* Key Metrics - Responsive Grid */}
            {metrics && (
              <div className="grid grid-cols-1 gap-3 xs:grid-cols-2">
                <div className="p-3 min-w-0 rounded-lg glass-card">
                  <div className="text-xs text-foreground-muted">Balance</div>
                  <div className="text-sm font-bold truncate sm:text-base text-foreground">
                    {metrics.balance.toFixed(2)} ETH
                  </div>
                </div>
                <div className="p-3 min-w-0 rounded-lg glass-card">
                  <div className="text-xs text-foreground-muted">Transactions</div>
                  <div className="text-sm font-bold truncate sm:text-base text-foreground">
                    {metrics.transactionCount.toLocaleString()}
                  </div>
                </div>
                <div className="p-3 min-w-0 rounded-lg glass-card">
                  <div className="text-xs text-foreground-muted">Tokens</div>
                  <div className="text-sm font-bold sm:text-base text-foreground">
                    {metrics.tokenCount}
                  </div>
                </div>
                <div className="p-3 min-w-0 rounded-lg glass-card">
                  <div className="text-xs text-foreground-muted">Age (days)</div>
                  <div className="text-sm font-bold sm:text-base text-foreground">
                    {metrics.walletAge}
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'risk':
        return riskScore ? (
          <div className="space-y-4">
            {/* Overall Risk Score */}
            <div className="p-4 rounded-lg glass-card">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-semibold">Overall Risk Score</h4>
                <span className={`px-3 py-1 rounded-full text-xs font-bold ${getRiskColor(riskScore.totalScore)}`}>
                  {getRiskLevel(riskScore.totalScore)}
                </span>
              </div>
              <div className="relative">
                <div className="w-full h-3 rounded-full bg-background-tertiary">
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${
                      riskScore.totalScore >= 80 ? 'bg-red-500' :
                      riskScore.totalScore >= 60 ? 'bg-orange-500' :
                      riskScore.totalScore >= 40 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${riskScore.totalScore}%` }}
                  ></div>
                </div>
                <div className="mt-2 text-2xl font-bold text-center text-foreground">
                  {riskScore.totalScore}/100
                </div>
              </div>
            </div>

            {/* Risk Level Badge */}
            <div className="p-3 rounded-lg glass-card">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Risk Level:</span>
                <span className={`px-3 py-1 rounded-full text-xs font-bold uppercase ${
                  riskScore.riskLevel === 'critical' ? 'bg-red-500/20 text-red-400' :
                  riskScore.riskLevel === 'high' ? 'bg-orange-500/20 text-orange-400' :
                  riskScore.riskLevel === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-green-500/20 text-green-400'
                }`}>
                  {riskScore.riskLevel}
                </span>
              </div>
            </div>

            {/* Risk Factors */}
            <div className="space-y-3 table-container" style={{ maxHeight: '300px' }}>
              {Object.entries(riskScore.factors).map(([factor, score]) => (
                <div key={factor} className="p-3 rounded-lg glass-card scrollable-list-item"
                     style={{
                       scrollMarginTop: '0.5rem',
                       scrollMarginBottom: '0.5rem'
                     }}>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium capitalize">{factor}</span>
                    <span className="text-sm font-bold">{score}/100</span>
                  </div>
                  <div className="w-full h-2 rounded-full bg-background-tertiary">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        score >= 80 ? 'bg-red-500' :
                        score >= 60 ? 'bg-orange-500' :
                        score >= 40 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${score}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Risk Flags */}
            {riskScore.flags.length > 0 && (
              <div className="p-3 rounded-lg glass-card">
                <h4 className="flex gap-2 items-center mb-2 text-sm font-semibold">
                  <FaExclamationTriangle className="text-red-400" />
                  Risk Flags
                </h4>
                <div className="space-y-2">
                  {riskScore.flags.map((flag, index) => (
                    <div key={index} className="flex gap-2 items-center text-sm">
                      <div className="flex-shrink-0 w-2 h-2 bg-red-400 rounded-full"></div>
                      <span className="text-foreground-muted">{flag}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Last Updated */}
            <div className="p-3 rounded-lg glass-card">
              <div className="flex justify-between items-center text-sm">
                <span className="text-foreground-muted">Last Updated:</span>
                <span className="text-foreground">{riskScore.lastUpdated.toLocaleString()}</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-4 rounded-lg glass-card">
            <div className="text-center text-foreground-muted">
              <FaShieldAlt className="mx-auto mb-2 text-2xl" />
              <p>No risk assessment available</p>
              <p className="mt-1 text-xs">Risk analysis will be performed when wallet data is loaded</p>
            </div>
          </div>
        );

      case 'activity':
        return metrics ? (
          <div className="space-y-4">
            <div className="p-3 rounded-lg glass-card">
              <h4 className="mb-3 text-sm font-semibold">Activity Timeline</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-foreground-muted">First Seen:</span>
                  <span className="text-foreground">{metrics.firstSeen}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Last Activity:</span>
                  <span className="text-foreground">{metrics.lastActivity}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Avg Transaction:</span>
                  <span className="text-foreground">{metrics.avgTransactionValue.toFixed(2)} ETH</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Counterparties:</span>
                  <span className="text-foreground">{metrics.uniqueCounterparties}</span>
                </div>
              </div>
            </div>
          </div>
        ) : null;

      case 'connections':
        return (
          <div className="space-y-4">
            <div className="p-3 rounded-lg glass-card">
              <h4 className="mb-3 text-sm font-semibold">Network Analysis</h4>
              <div className="text-sm text-foreground-muted">
                Connection analysis and network mapping features will be displayed here.
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!selectedWallet) return null;

  // For embedded mode, use simple styling
  if (embedded) {
    return (
      <div className="flex flex-col h-full bg-slate-800">
        {/* Simple header */}
        <div className="flex justify-between items-center p-4 border-b border-slate-600">
          <div className="flex gap-3 items-center">
            <div className="flex justify-center items-center w-8 h-8 bg-blue-500 rounded-lg">
              <FaWallet className="text-sm text-white" />
            </div>
            <div>
              <h3 className="font-bold text-white">Risk Analysis</h3>
              <p className="font-mono text-xs text-gray-400">
                {selectedWallet.address.slice(0, 10)}...{selectedWallet.address.slice(-8)}
              </p>
            </div>
          </div>
        </div>

        {/* Simple tab navigation */}
        <div className="flex border-b border-slate-600">
          {[
            { id: 'overview', label: 'Overview', icon: FaChartLine },
            { id: 'risk', label: 'Risk', icon: FaShieldAlt },
            { id: 'activity', label: 'Activity', icon: FaClock },
            { id: 'connections', label: 'Network', icon: FaNetworkWired }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 p-3 text-xs font-medium transition-all duration-200 flex items-center justify-center gap-1 ${
                activeTab === tab.id
                  ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-500/10'
                  : 'text-gray-400 hover:text-white hover:bg-slate-700'
              }`}
            >
              <tab.icon size={12} />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Simple content */}
        <div className="overflow-y-auto flex-1 p-4 scrollable-container">
          {renderTabContent()}
        </div>

        {/* Simple action buttons */}
        <div className="p-4 border-t border-slate-600">
          <div className="flex gap-3">
            <button className="flex-1 px-4 py-3 text-sm font-semibold text-white bg-gradient-to-r from-red-500 to-pink-500 rounded-xl shadow-lg transition-all duration-300 transform hover:from-red-600 hover:to-pink-600 shadow-red-500/25 hover:shadow-red-500/40 hover:scale-105">
              <FaFlag className="mr-2" />
              Report
            </button>
            <button
              onClick={isInWatchList ? handleRemoveFromWatchList : handleAddToWatchList}
              disabled={isAddingToWatchList || isRemovingFromWatchList}
              className={`flex-1 text-sm py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:scale-105 transform ${
                isInWatchList
                  ? 'text-white bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-gray-500 hover:to-gray-600 shadow-yellow-500/25 hover:shadow-gray-500/25'
                  : 'text-white bg-gradient-to-r from-gray-500 to-slate-500 hover:from-yellow-500 hover:to-amber-500 shadow-gray-500/25 hover:shadow-yellow-500/25'
              } ${(isAddingToWatchList || isRemovingFromWatchList) ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isAddingToWatchList ? (
                <>
                  <div className="inline-block mr-2 w-4 h-4 rounded-full border-2 border-white animate-spin border-t-transparent"></div>
                  Adding...
                </>
              ) : isRemovingFromWatchList ? (
                <>
                  <div className="inline-block mr-2 w-4 h-4 rounded-full border-2 border-white animate-spin border-t-transparent"></div>
                  Removing...
                </>
              ) : isInWatchList ? (
                <>
                  <FaStar className="mr-2" />
                  Watching
                </>
              ) : (
                <>
                  <FaRegStar className="mr-2" />
                  Watch
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Get position from layout manager
  const position = getComponentPosition('walletAnalysis');

  // Build responsive classes
  const getResponsiveClasses = () => {
    if (layout.isMobile) {
      return 'glass-card rounded-xl border border-border-accent shadow-glow overflow-hidden';
    } else {
      return 'glass-card rounded-xl border border-border-accent shadow-glow overflow-hidden';
    }
  };

  // Mobile: Full-screen modal
  if (layout.isMobile) {
    return (
      <div
        className={`${position.position} ${getResponsiveClasses()}`}
        style={{
          zIndex: position.zIndex,
          width: position.width,
          maxWidth: position.maxWidth,
          maxHeight: position.maxHeight,
          overflow: position.overflow
        }}
      >
        {/* Mobile header with close button */}
        <div className="flex justify-between items-center p-4 bg-gradient-to-r border-b border-border-accent from-primary-600/10 via-accent-600/10 to-secondary-600/10">
          <div className="flex gap-3 items-center">
            <div className="flex justify-center items-center w-10 h-10 rounded-full bg-gradient-brand">
              <FaWallet className="text-sm text-white" />
            </div>
            <div>
              <h3 className="font-bold text-foreground">Wallet Analysis</h3>
              <p className="text-xs text-foreground-muted font-mono truncate max-w-[200px]">
                {selectedWallet.address.slice(0, 10)}...{selectedWallet.address.slice(-8)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg transition-colors hover:bg-background-tertiary"
          >
            <FaTimes className="text-foreground-muted" />
          </button>
        </div>

        {/* Mobile tab navigation */}
        <div className="flex overflow-x-auto border-b border-border-accent">
          {[
            { id: 'overview', label: 'Overview', icon: FaChartLine },
            { id: 'risk', label: 'Risk', icon: FaShieldAlt },
            { id: 'activity', label: 'Activity', icon: FaClock },
            { id: 'connections', label: 'Network', icon: FaNetworkWired }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-shrink-0 px-4 py-3 text-xs font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                activeTab === tab.id
                  ? 'text-accent-400 border-b-2 border-accent-400 bg-accent-500/10'
                  : 'text-foreground-muted hover:text-foreground hover:bg-background-tertiary'
              }`}
            >
              <tab.icon size={12} />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Mobile content area */}
        <div className="overflow-y-auto flex-1 p-4">
          {/* Content will be rendered here - same as desktop */}
          {renderTabContent()}
        </div>

        {/* Mobile action buttons */}
        <div className="p-4 border-t border-border-accent bg-background-secondary/50">
          <div className="flex gap-2">
            <button className="flex-1 py-2 text-xs btn-primary">
              <FaFlag className="mr-1" />
              Report
            </button>
            <button
              onClick={isInWatchList ? handleRemoveFromWatchList : handleAddToWatchList}
              disabled={isAddingToWatchList || isRemovingFromWatchList}
              className={`flex-1 text-xs py-2 transition-all duration-300 ${
                isInWatchList
                  ? 'text-white bg-gradient-to-r from-yellow-500 to-amber-500 border-yellow-500 hover:from-gray-500 hover:to-gray-600 hover:border-gray-500'
                  : 'text-white bg-gradient-to-r from-gray-500 border-gray-500 to-slate-500 hover:from-yellow-500 hover:to-amber-500 hover:border-yellow-500'
              } ${(isAddingToWatchList || isRemovingFromWatchList) ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isAddingToWatchList ? (
                <>
                  <div className="inline-block mr-1 w-3 h-3 rounded-full border border-white animate-spin border-t-transparent"></div>
                  Adding...
                </>
              ) : isRemovingFromWatchList ? (
                <>
                  <div className="inline-block mr-1 w-3 h-3 rounded-full border border-white animate-spin border-t-transparent"></div>
                  Removing...
                </>
              ) : isInWatchList ? (
                <>
                  <FaStar className="mr-1" />
                  Watching
                </>
              ) : (
                <>
                  <FaRegStar className="mr-1" />
                  Watch
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Desktop and tablet layout
  return (
    <div
      className={`${position.position} ${getResponsiveClasses()}`}
      style={{
        zIndex: position.zIndex,
        width: position.width,
        maxWidth: position.maxWidth,
        maxHeight: position.maxHeight,
        overflow: position.overflow
      }}
    >
      {/* Header */}
      <div className="p-4 bg-gradient-to-r border-b border-border-accent from-primary-600/10 via-accent-600/10 to-secondary-600/10">
        <div className="flex justify-between items-center">
          <div className="flex gap-3 items-center">
            <div className="flex justify-center items-center w-10 h-10 rounded-full bg-gradient-brand">
              <FaWallet className="text-sm text-white" />
            </div>
            <div>
              <h3 className="font-bold text-foreground">Wallet Analysis</h3>
              <p className="text-xs text-foreground-muted font-mono truncate max-w-[200px] sm:max-w-none">
                {selectedWallet.address.slice(0, 10)}...{selectedWallet.address.slice(-8)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg transition-colors hover:bg-background-tertiary"
          >
            ×
          </button>
        </div>
      </div>

      {/* Tab Navigation with Tooltips */}
      <div className="flex border-b border-border-accent">
        {[
          {
            id: 'overview',
            label: 'Overview',
            icon: FaChartLine,
            tooltip: 'Wallet overview with balance, tokens, and classification',
            tooltipVi: 'Tổng quan ví với số dư, token và phân loại'
          },
          {
            id: 'risk',
            label: 'Risk',
            icon: FaShieldAlt,
            tooltip: 'Risk assessment and security analysis',
            tooltipVi: 'Đánh giá rủi ro và phân tích bảo mật'
          },
          {
            id: 'activity',
            label: 'Activity',
            icon: FaClock,
            tooltip: 'Transaction history and activity patterns',
            tooltipVi: 'Lịch sử giao dịch và mẫu hoạt động'
          },
          {
            id: 'connections',
            label: 'Network',
            icon: FaNetworkWired,
            tooltip: 'Network connections and relationship analysis',
            tooltipVi: 'Kết nối mạng và phân tích mối quan hệ'
          }
        ].map(tab => (
          <Tooltip
            key={tab.id}
            content={tab.tooltip}
            contentVi={tab.tooltipVi}
            position="bottom"
          >
            <button
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 p-3 text-xs font-medium transition-all duration-200 flex items-center justify-center gap-1 hover:scale-105 ${
                activeTab === tab.id
                  ? 'text-accent-400 border-b-2 border-accent-400 bg-accent-500/10'
                  : 'text-foreground-muted hover:text-foreground hover:bg-background-tertiary'
              }`}
            >
              <tab.icon size={12} />
              {tab.label}
            </button>
          </Tooltip>
        ))}
      </div>

      {/* Content - Enhanced Scrollable */}
      <div className="overflow-y-auto flex-1 p-4 scrollable-container"
           style={{
             scrollBehavior: 'smooth',
             WebkitOverflowScrolling: 'touch',
             overscrollBehavior: 'contain'
           }}>
        {renderTabContent()}
      </div>

      {/* Action Buttons with Tooltips */}
      <div className="p-4 border-t border-border-accent bg-background-secondary/50">
        <div className="flex gap-2">
          <Tooltip
            content="Report this wallet for suspicious activity to the community database"
            contentVi="Báo cáo ví này về hoạt động đáng ngờ cho cơ sở dữ liệu cộng đồng"
            showShortcut="Ctrl+R"
          >
            <button className="flex-1 py-2 text-xs transition-transform btn-primary hover:scale-105">
              <FaFlag className="mr-1" />
              Report
            </button>
          </Tooltip>
          <Tooltip
            content={isAddingToWatchList
              ? "Adding to watch list..."
              : isRemovingFromWatchList
                ? "Removing from watch list..."
                : isInWatchList
                  ? "Remove this wallet from your watch list"
                  : "Add this wallet to your monitoring watchlist for real-time alerts"
            }
            contentVi={isAddingToWatchList
              ? "Đang thêm vào danh sách theo dõi..."
              : isRemovingFromWatchList
                ? "Đang xóa khỏi danh sách theo dõi..."
                : isInWatchList
                  ? "Xóa ví này khỏi danh sách theo dõi của bạn"
                  : "Thêm ví này vào danh sách theo dõi để nhận cảnh báo thời gian thực"
            }
            showShortcut="Ctrl+M"
          >
            <button
              onClick={isInWatchList ? handleRemoveFromWatchList : handleAddToWatchList}
              disabled={isAddingToWatchList || isRemovingFromWatchList}
              className={`flex-1 text-xs py-2 hover:scale-105 transition-all duration-300 ${
                isInWatchList
                  ? 'text-white bg-gradient-to-r from-yellow-500 to-amber-500 border-yellow-500 hover:from-gray-500 hover:to-gray-600 hover:border-gray-500'
                  : 'text-white bg-gradient-to-r from-gray-500 border-gray-500 to-slate-500 hover:from-yellow-500 hover:to-amber-500 hover:border-yellow-500'
              } ${(isAddingToWatchList || isRemovingFromWatchList) ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isAddingToWatchList ? (
                <>
                  <div className="inline-block mr-1 w-3 h-3 rounded-full border border-white animate-spin border-t-transparent"></div>
                  Adding...
                </>
              ) : isRemovingFromWatchList ? (
                <>
                  <div className="inline-block mr-1 w-3 h-3 rounded-full border border-white animate-spin border-t-transparent"></div>
                  Removing...
                </>
              ) : isInWatchList ? (
                <>
                  <FaStar className="mr-1" />
                  Watching
                </>
              ) : (
                <>
                  <FaRegStar className="mr-1" />
                  Watch
                </>
              )}
            </button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default WalletAnalysisPanel;
