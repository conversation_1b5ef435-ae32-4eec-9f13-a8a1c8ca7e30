import React, { useState, useEffect } from 'react';
import { 
  FaTimes, FaExternalLinkAlt, FaCopy, FaCheck, FaGasPump, 
  FaClock, FaCoins, FaHashtag, FaExchangeAlt, FaShieldAlt,
  FaChevronDown, FaChevronUp, FaArrowRight, FaArrowLeft
} from 'react-icons/fa';
import { MoneyFlowTransaction } from '@/services/moneyFlowService';

interface TransactionFlowDetailsProps {
  transactions: MoneyFlowTransaction[];
  isOpen: boolean;
  onClose: () => void;
  title?: string;
}

const TransactionFlowDetails: React.FC<TransactionFlowDetailsProps> = ({
  transactions,
  isOpen,
  onClose,
  title = "Transaction Flow Details"
}) => {
  const [expandedTx, setExpandedTx] = useState<string | null>(null);
  const [copiedHash, setCopiedHash] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'timestamp' | 'value' | 'gasUsed'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    if (copiedHash) {
      const timer = setTimeout(() => setCopiedHash(null), 2000);
      return () => clearTimeout(timer);
    }
  }, [copiedHash]);

  if (!isOpen) return null;

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedHash(type);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  const formatTokenAmount = (value: number, symbol?: string, decimals?: number) => {
    const formatted = decimals ? (value / Math.pow(10, decimals)).toFixed(6) : value.toFixed(6);
    return `${formatted} ${symbol || 'ETH'}`;
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'low': return 'text-green-400 bg-green-400/10 border-green-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'swap': return <FaExchangeAlt className="text-blue-400" />;
      case 'transfer': return <FaArrowRight className="text-green-400" />;
      case 'mint': return <FaCoins className="text-yellow-400" />;
      case 'burn': return <FaCoins className="text-red-400" />;
      default: return <FaExchangeAlt className="text-gray-400" />;
    }
  };

  const sortedTransactions = [...transactions].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case 'timestamp':
        aValue = new Date(a.timestamp).getTime();
        bValue = new Date(b.timestamp).getTime();
        break;
      case 'value':
        aValue = a.usdValue || 0;
        bValue = b.usdValue || 0;
        break;
      case 'gasUsed':
        aValue = a.gasUsed;
        bValue = b.gasUsed;
        break;
      default:
        return 0;
    }

    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
  });

  const totalValue = transactions.reduce((sum, tx) => sum + (tx.usdValue || 0), 0);
  const totalGasFees = transactions.reduce((sum, tx) => sum + tx.gasFee, 0);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="w-full max-w-4xl max-h-[90vh] mx-4 bg-background-secondary rounded-xl border border-border shadow-2xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-blue-500">
              <FaExchangeAlt className="text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-foreground">{title}</h2>
              <p className="text-sm text-foreground-muted">
                {transactions.length} transactions • {formatCurrency(totalValue)} total value
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-background-tertiary transition-colors"
          >
            <FaTimes className="text-foreground-muted" />
          </button>
        </div>

        {/* Summary Stats */}
        <div className="p-6 border-b border-border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-background-tertiary/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <FaCoins className="text-green-400" />
                <span className="text-sm font-medium text-foreground-muted">Total Value</span>
              </div>
              <div className="text-xl font-bold text-foreground">{formatCurrency(totalValue)}</div>
            </div>
            <div className="bg-background-tertiary/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <FaGasPump className="text-orange-400" />
                <span className="text-sm font-medium text-foreground-muted">Total Gas Fees</span>
              </div>
              <div className="text-xl font-bold text-foreground">{totalGasFees.toFixed(4)} ETH</div>
            </div>
            <div className="bg-background-tertiary/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <FaHashtag className="text-blue-400" />
                <span className="text-sm font-medium text-foreground-muted">Transactions</span>
              </div>
              <div className="text-xl font-bold text-foreground">{transactions.length}</div>
            </div>
          </div>
        </div>

        {/* Sort Controls */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium text-foreground-muted">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-1 text-sm bg-background border border-border rounded-lg focus:ring-2 focus:ring-accent-400 focus:border-transparent"
            >
              <option value="timestamp">Time</option>
              <option value="value">Value</option>
              <option value="gasUsed">Gas Used</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="flex items-center gap-1 px-3 py-1 text-sm font-medium text-foreground-muted hover:text-foreground transition-colors"
            >
              {sortOrder === 'asc' ? <FaChevronUp /> : <FaChevronDown />}
              {sortOrder === 'asc' ? 'Ascending' : 'Descending'}
            </button>
          </div>
        </div>

        {/* Transaction List */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-3">
            {sortedTransactions.map((tx) => (
              <div
                key={tx.id}
                className="bg-background-tertiary/30 rounded-lg border border-border hover:border-accent-400/30 transition-colors"
              >
                {/* Transaction Header */}
                <div
                  className="p-4 cursor-pointer"
                  onClick={() => setExpandedTx(expandedTx === tx.id ? null : tx.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-background-secondary">
                        {getTransactionIcon(tx.transactionType)}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-foreground">{tx.method || 'Transfer'}</span>
                          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getRiskColor(tx.riskLevel)}`}>
                            {tx.riskLevel}
                          </span>
                        </div>
                        <div className="text-sm text-foreground-muted">
                          {formatTimestamp(tx.timestamp)}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-foreground">
                        {formatCurrency(tx.usdValue || 0)}
                      </div>
                      <div className="text-sm text-foreground-muted">
                        {formatTokenAmount(tx.value, tx.tokenSymbol, tx.tokenDecimals)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                {expandedTx === tx.id && (
                  <div className="px-4 pb-4 border-t border-border">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      {/* Transaction Hash */}
                      <div>
                        <label className="block text-xs font-medium text-foreground-muted mb-1">Transaction Hash</label>
                        <div className="flex items-center gap-2 p-2 bg-background-secondary rounded-lg">
                          <span className="font-mono text-sm text-foreground flex-1 truncate">{tx.hash}</span>
                          <button
                            onClick={() => copyToClipboard(tx.hash, `hash-${tx.id}`)}
                            className="flex items-center justify-center w-6 h-6 rounded hover:bg-background-tertiary transition-colors"
                          >
                            {copiedHash === `hash-${tx.id}` ? (
                              <FaCheck className="text-green-400 text-xs" />
                            ) : (
                              <FaCopy className="text-foreground-muted text-xs" />
                            )}
                          </button>
                          <a
                            href={`https://etherscan.io/tx/${tx.hash}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center justify-center w-6 h-6 rounded hover:bg-background-tertiary transition-colors"
                          >
                            <FaExternalLinkAlt className="text-foreground-muted text-xs" />
                          </a>
                        </div>
                      </div>

                      {/* Block Number */}
                      <div>
                        <label className="block text-xs font-medium text-foreground-muted mb-1">Block Number</label>
                        <div className="p-2 bg-background-secondary rounded-lg">
                          <span className="font-mono text-sm text-foreground">{tx.blockNumber.toLocaleString()}</span>
                        </div>
                      </div>

                      {/* From Address */}
                      <div>
                        <label className="block text-xs font-medium text-foreground-muted mb-1">From</label>
                        <div className="flex items-center gap-2 p-2 bg-background-secondary rounded-lg">
                          <span className="font-mono text-sm text-foreground flex-1">{formatAddress(tx.from)}</span>
                          <button
                            onClick={() => copyToClipboard(tx.from, `from-${tx.id}`)}
                            className="flex items-center justify-center w-6 h-6 rounded hover:bg-background-tertiary transition-colors"
                          >
                            {copiedHash === `from-${tx.id}` ? (
                              <FaCheck className="text-green-400 text-xs" />
                            ) : (
                              <FaCopy className="text-foreground-muted text-xs" />
                            )}
                          </button>
                        </div>
                      </div>

                      {/* To Address */}
                      <div>
                        <label className="block text-xs font-medium text-foreground-muted mb-1">To</label>
                        <div className="flex items-center gap-2 p-2 bg-background-secondary rounded-lg">
                          <span className="font-mono text-sm text-foreground flex-1">{formatAddress(tx.to)}</span>
                          <button
                            onClick={() => copyToClipboard(tx.to, `to-${tx.id}`)}
                            className="flex items-center justify-center w-6 h-6 rounded hover:bg-background-tertiary transition-colors"
                          >
                            {copiedHash === `to-${tx.id}` ? (
                              <FaCheck className="text-green-400 text-xs" />
                            ) : (
                              <FaCopy className="text-foreground-muted text-xs" />
                            )}
                          </button>
                        </div>
                      </div>

                      {/* Gas Details */}
                      <div>
                        <label className="block text-xs font-medium text-foreground-muted mb-1">Gas Details</label>
                        <div className="p-2 bg-background-secondary rounded-lg">
                          <div className="text-sm text-foreground">
                            <div>Used: {tx.gasUsed.toLocaleString()}</div>
                            <div>Price: {tx.gasPrice.toFixed(2)} Gwei</div>
                            <div>Fee: {tx.gasFee.toFixed(6)} ETH</div>
                          </div>
                        </div>
                      </div>

                      {/* Token Details */}
                      {tx.token && (
                        <div>
                          <label className="block text-xs font-medium text-foreground-muted mb-1">Token Details</label>
                          <div className="p-2 bg-background-secondary rounded-lg">
                            <div className="text-sm text-foreground">
                              <div>Symbol: {tx.tokenSymbol}</div>
                              <div>Amount: {formatTokenAmount(tx.value, tx.tokenSymbol, tx.tokenDecimals)}</div>
                              <div>USD Value: {formatCurrency(tx.usdValue || 0)}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionFlowDetails;
