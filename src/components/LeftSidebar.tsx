import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { FaShieldAlt, FaTimes, FaFilter, FaEye, FaRobot } from 'react-icons/fa';
import SecurityAlertSystem from './SecurityAlertSystem';
import RiskFilterPanel from './RiskFilterPanel';
import { Node } from '@/services/neo4jService';
import { useLayout } from './LayoutManager';

// Import ClientOnlyWatchListPanel to avoid SSR issues
import ClientOnlyWatchListPanel from './ClientOnlyWatchListPanel';
import ClientOnlyAskAIPanel from './ClientOnlyAskAIPanel';

interface RiskFilterSettings {
  minRiskLevel: 'low' | 'medium' | 'high' | 'critical';
  showOnlyFlagged: boolean;
  hideWhitelisted: boolean;
  highlightSuspicious: boolean;
  autoHideClean: boolean;
  riskThreshold: number;
  filterTypes: {
    phishing: boolean;
    mev: boolean;
    laundering: boolean;
    sanctions: boolean;
    scam: boolean;
    suspicious: boolean;
  };
}

interface LeftSidebarProps {
  selectedWallet: Node | null;
  showSecurityAlerts: boolean;
  showRiskFilter: boolean;
  showWatchList: boolean;
  showAskAI: boolean;
  riskFilterSettings: RiskFilterSettings;
  forceActiveTab?: 'alerts' | 'riskFilter' | 'watchList' | 'askAI' | null;
  onAlertSelect?: (alert: any) => void;
  onRiskFilterChange?: (settings: RiskFilterSettings) => void;
  onApplyRiskFilter?: (settings: RiskFilterSettings) => void;
  onResetRiskFilter?: () => void;
  onWalletSelect?: (wallet: Node) => void;
  onAddToWatchList?: (address: string) => void;
  onFocusWallet?: (address: string) => void;
  totalWallets?: number;
  filteredWallets?: number;
  flaggedWallets?: number;
}

interface TooltipState {
  isVisible: boolean;
  content: string;
  contentVi?: string;
  position: { top: number; left: number };
  targetId: string;
}

interface TooltipConfig {
  content: string;
  contentVi?: string;
  showShortcut?: string;
}

const LeftSidebar: React.FC<LeftSidebarProps> = ({
  selectedWallet,
  showSecurityAlerts,
  showRiskFilter,
  showWatchList,
  showAskAI,
  riskFilterSettings,
  forceActiveTab,
  onAlertSelect,
  onRiskFilterChange,
  onApplyRiskFilter,
  onResetRiskFilter,
  onWalletSelect,
  onAddToWatchList,
  onFocusWallet,
  totalWallets = 0,
  filteredWallets = 0,
  flaggedWallets = 0
}) => {
  const { layout, getComponentPosition } = useLayout();

  // State persistence for sidebar state - Fixed hydration issue
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState<'alerts' | 'riskFilter' | 'watchList' | 'askAI'>('alerts');
  const [isHydrated, setIsHydrated] = useState(false);

  // Unified tooltip state management
  const [tooltip, setTooltip] = useState<TooltipState | null>(null);
  const [isVietnamese, setIsVietnamese] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const elementRefs = useRef<{ [key: string]: HTMLElement | null }>({});
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Remove unused height calculation - now handled by LayoutManager

  // Hydration and state initialization
  useEffect(() => {
    // Load persisted state after hydration
    if (typeof window !== 'undefined') {
      const savedCollapsed = localStorage.getItem('leftSidebar_isCollapsed');
      const savedActiveTab = localStorage.getItem('leftSidebar_activeTab');
      const lang = localStorage.getItem('language') || 'en';

      if (savedCollapsed) {
        setIsCollapsed(JSON.parse(savedCollapsed));
      }
      if (savedActiveTab) {
        setActiveTab(JSON.parse(savedActiveTab));
      }
      setIsVietnamese(lang === 'vi');
      setIsHydrated(true);
    }
  }, []);

  // Persist sidebar state changes (only after hydration)
  useEffect(() => {
    if (isHydrated && typeof window !== 'undefined') {
      localStorage.setItem('leftSidebar_isCollapsed', JSON.stringify(isCollapsed));
    }
  }, [isCollapsed, isHydrated]);

  useEffect(() => {
    if (isHydrated && typeof window !== 'undefined') {
      localStorage.setItem('leftSidebar_activeTab', JSON.stringify(activeTab));
    }
  }, [activeTab, isHydrated]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);



  // Height is now managed by LayoutManager - no need for manual calculation

  // Tooltip configurations for all sidebar elements
  const tooltipConfigs: { [key: string]: TooltipConfig } = {
    collapseButton: {
      content: isCollapsed ? "Expand sidebar" : "Collapse sidebar",
      contentVi: isCollapsed ? "Mở rộng thanh bên" : "Thu gọn thanh bên"
    },
    alertsCollapsed: {
      content: "Security Alerts",
      contentVi: "Cảnh báo Bảo mật"
    },

    riskFilterCollapsed: {
      content: "Risk Filter",
      contentVi: "Bộ lọc Rủi ro"
    },
    watchListCollapsed: {
      content: "Watch List",
      contentVi: "Danh sách Theo dõi"
    },
    askAICollapsed: {
      content: "Ask AI Assistant",
      contentVi: "Hỏi Trợ lý AI"
    },
    alertsTab: {
      content: "Security alerts and threat monitoring for suspicious activities",
      contentVi: "Cảnh báo bảo mật và giám sát mối đe dọa cho các hoạt động đáng ngờ"
    },

    riskFilterTab: {
      content: "Risk-based filtering system to highlight suspicious wallets and transactions",
      contentVi: "Hệ thống lọc dựa trên rủi ro để làm nổi bật các ví và giao dịch đáng ngờ"
    },
    watchListTab: {
      content: "Monitor and track specific wallets with custom alerts and thresholds",
      contentVi: "Giám sát và theo dõi các ví cụ thể với cảnh báo và ngưỡng tùy chỉnh"
    },
    askAITab: {
      content: "AI-powered blockchain analysis and insights for wallet investigation",
      contentVi: "Phân tích blockchain và thông tin chi tiết được hỗ trợ bởi AI để điều tra ví"
    }
  };

  // Calculate the bubble map viewport area for tooltip positioning
  const calculateMapViewport = () => {
    // Default values for SSR
    if (typeof window === 'undefined') {
      return {
        left: 400,
        top: 100,
        right: 1200,
        bottom: 700,
        width: 800,
        height: 600,
      };
    }

    const viewport = { width: window.innerWidth, height: window.innerHeight };
    const headerHeight = 80; // TopBar height
    const topBarBuffer = 40; // Extra buffer below TopBar for tooltip safety
    const effectiveTopMargin = headerHeight + topBarBuffer; // 120px total
    const sidebarWidth = calculateSidebarWidth();
    const sidebarLeft = viewport.width < 640 ? 16 : 24; // left-4 sm:left-6

    // Calculate available map area (excluding TopBar buffer and sidebar)
    const mapLeft = sidebarLeft + sidebarWidth + 16; // Sidebar + margin
    const mapTop = effectiveTopMargin + 16; // TopBar + buffer + margin
    const mapRight = viewport.width - 16; // Viewport - margin
    const mapBottom = viewport.height - 16; // Viewport - margin

    return {
      left: mapLeft,
      top: mapTop,
      right: mapRight,
      bottom: mapBottom,
      width: mapRight - mapLeft,
      height: mapBottom - mapTop,
    };
  };



  // Enhanced tooltip positioning that accounts for sidebar responsive width
  const calculateSidebarWidth = () => {
    if (isCollapsed) return 48; // w-12 = 48px

    // Default for SSR and before hydration
    if (typeof window === 'undefined' || !isHydrated) {
      return 384; // Default desktop width
    }

    const viewport = window.innerWidth;
    if (viewport < 640) { // sm breakpoint
      // Mobile: w-[calc(100vw-1rem)] max-w-sm
      const calcWidth = viewport - 16; // 1rem = 16px
      const maxWidth = 384; // max-w-sm = 384px
      return Math.min(calcWidth, maxWidth);
    } else {
      // Desktop: sm:w-96 = 384px
      return 384;
    }
  };

  // Unified tooltip management functions with center-right map positioning
  const showTooltip = (targetId: string, delay: number = 500) => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      const targetElement = elementRefs.current[targetId];
      const config = tooltipConfigs[targetId];

      if (!targetElement || !config) return;

      // Get the map viewport area
      const mapArea = calculateMapViewport();

      // Calculate center-right position in the map area
      // 75% from left edge, 50% from top edge of the map area
      const centerRightX = mapArea.left + (mapArea.width * 0.75);
      const centerY = mapArea.top + (mapArea.height * 0.5);

      let top = centerY;
      let left = centerRightX;

      // Responsive adjustments for mobile - prevent hydration issues
      if (isHydrated && typeof window !== 'undefined' && window.innerWidth < 640) {
        // On mobile, position slightly more to the left to avoid edge
        left = mapArea.left + (mapArea.width * 0.65);
        // Ensure minimum distance from edges
        const minMargin = 20;
        if (left + 200 > mapArea.right - minMargin) {
          left = mapArea.right - 200 - minMargin;
        }
        if (left < mapArea.left + minMargin) {
          left = mapArea.left + minMargin;
        }
      }

      // Ensure tooltip stays within map bounds
      const tooltipWidth = 200; // Estimated tooltip width
      const tooltipHeight = 40; // Estimated tooltip height

      // Horizontal bounds checking
      if (left + tooltipWidth > mapArea.right - 16) {
        left = mapArea.right - tooltipWidth - 16;
      }
      if (left < mapArea.left + 16) {
        left = mapArea.left + 16;
      }

      // Vertical bounds checking
      if (top + tooltipHeight > mapArea.bottom - 16) {
        top = mapArea.bottom - tooltipHeight - 16;
      }
      if (top < mapArea.top + 16) {
        top = mapArea.top + 16;
      }

      const displayContent = isVietnamese && config.contentVi ? config.contentVi : config.content;

      setTooltip({
        isVisible: true,
        content: displayContent,
        contentVi: config.contentVi,
        position: { top, left },
        targetId
      });
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setTooltip(null);
  };

  // Element ref setter
  const setElementRef = (id: string) => (element: HTMLElement | null) => {
    elementRefs.current[id] = element;
  };

  // Event handlers for unified tooltip system
  const createTooltipHandlers = (targetId: string) => ({
    onMouseEnter: () => showTooltip(targetId),
    onMouseLeave: hideTooltip,
    onFocus: () => showTooltip(targetId, 0),
    onBlur: hideTooltip,
    onTouchStart: () => {
      if (typeof window !== 'undefined' && window.innerWidth < 768) {
        showTooltip(targetId, 0);
      }
    },
    onTouchEnd: () => {
      if (typeof window !== 'undefined' && window.innerWidth < 768) {
        setTimeout(hideTooltip, 2000);
      }
    }
  });

  // Determine which panels to show (before early return)
  const activePanels = [showSecurityAlerts, showRiskFilter, showWatchList, showAskAI].filter(Boolean).length;
  const showMultiplePanels = activePanels > 1;
  const showOnlyAlerts = showSecurityAlerts && !showRiskFilter && !showWatchList && !showAskAI;
  const showOnlyRiskFilter = showRiskFilter && !showSecurityAlerts && !showWatchList && !showAskAI;
  const showOnlyWatchList = showWatchList && !showSecurityAlerts && !showRiskFilter && !showAskAI;
  const showOnlyAskAI = showAskAI && !showSecurityAlerts && !showRiskFilter && !showWatchList;

  // Auto-set active tab when only one panel is visible (hook must be before early return)
  useEffect(() => {
    if (showOnlyAlerts) {
      setActiveTab('alerts');
    } else if (showOnlyRiskFilter) {
      setActiveTab('riskFilter');
    } else if (showOnlyWatchList) {
      setActiveTab('watchList');
    } else if (showOnlyAskAI) {
      setActiveTab('askAI');
    }
  }, [showOnlyAlerts, showOnlyRiskFilter, showOnlyWatchList, showOnlyAskAI]);

  // Handle forced active tab changes from parent component
  useEffect(() => {
    if (forceActiveTab) {
      setActiveTab(forceActiveTab);
    }
  }, [forceActiveTab]);

  // Don't render if no panels are active (early return after all hooks)
  if (!showSecurityAlerts && !showRiskFilter && !showWatchList && !showAskAI) {
    return null;
  }

  // Get position from layout manager
  const position = getComponentPosition('leftSidebar');

  // Build responsive classes based on device type - aligned with BubbleMapContainer pattern
  const getResponsiveClasses = () => {
    if (layout.isMobile) {
      return isCollapsed
        ? 'w-0 overflow-hidden'
        : 'w-full max-h-[70vh] rounded-t-xl';
    } else if (layout.isTablet) {
      return isCollapsed
        ? 'w-12'
        : 'w-[calc(50vw-2rem)] max-w-sm';
    } else {
      // Desktop: Apply responsive pattern similar to BubbleMapContainer
      return isCollapsed
        ? 'w-12'
        : 'w-96';
    }
  };

  // Get responsive flex classes for internal components
  const getFlexClasses = () => {
    return 'flex-row hidden gap-3 md:flex';
  };

  // Enhanced responsive flex classes for tab navigation with better wrapping logic
  const getTabFlexClasses = () => {
    // Count active tabs to determine layout strategy
    const activeTabCount = [showSecurityAlerts, showRiskFilter, showWatchList, showAskAI].filter(Boolean).length;

    // For mobile and tablet, always use wrap for better text visibility
    if (layout.isMobile || layout.isTablet) {
      if (activeTabCount > 3) {
        return 'flex flex-wrap gap-1 justify-center';
      } else {
        return 'flex gap-1';
      }
    }

    // For desktop, use more sophisticated logic
    if (activeTabCount > 4) {
      // With 5+ tabs, use wrap with better spacing
      return 'flex flex-wrap gap-1 justify-start';
    } else if (activeTabCount > 3) {
      // With 4 tabs, use smaller padding and gap
      return 'flex gap-0.5';
    } else {
      // 3 or fewer tabs, normal layout
      return 'flex gap-1';
    }
  };

  // Get responsive tab button classes based on tab count
  const getTabButtonClasses = (_isActive: boolean, baseColorClasses: string) => {
    const activeTabCount = [showSecurityAlerts, showRiskFilter, showWatchList, showAskAI].filter(Boolean).length;

    // Base classes
    let classes = 'font-medium transition-all duration-200 flex items-center justify-center';

    // Responsive sizing based on tab count and device
    if (layout.isMobile) {
      if (activeTabCount > 4) {
        // 5+ tabs on mobile: smaller padding, smaller text, allow wrapping
        classes += ' p-1.5 text-xs gap-1 min-w-[60px] flex-1 max-w-[80px]';
      } else if (activeTabCount > 3) {
        // 4 tabs on mobile: medium padding
        classes += ' p-2 text-xs gap-1 flex-1 min-w-0';
      } else {
        // 3 or fewer tabs: normal mobile sizing
        classes += ' p-2 sm:p-3 text-xs sm:text-sm gap-1 sm:gap-2 flex-1 min-w-0';
      }
    } else if (layout.isTablet) {
      if (activeTabCount > 4) {
        // 5+ tabs on tablet: compact layout
        classes += ' p-1.5 text-xs gap-1 flex-1 min-w-[70px]';
      } else {
        // 4 or fewer tabs: normal tablet sizing
        classes += ' p-2 text-sm gap-1 flex-1 min-w-0';
      }
    } else {
      // Desktop
      if (activeTabCount > 4) {
        // 5+ tabs on desktop: very compact
        classes += ' p-1.5 lg:p-2 text-xs gap-1 flex-1 min-w-[80px]';
      } else if (activeTabCount > 3) {
        // 4 tabs on desktop: slightly compact
        classes += ' p-2 lg:p-2.5 text-xs lg:text-sm gap-1 flex-1 min-w-0';
      } else {
        // 3 or fewer tabs: normal desktop sizing
        classes += ' p-2 lg:p-3 text-xs lg:text-sm gap-1 lg:gap-2 flex-1 min-w-0';
      }
    }

    // Add color classes
    classes += ` ${baseColorClasses}`;

    return classes;
  };

  // Get optimized label text based on tab count and device
  const getTabLabel = (fullLabel: string, shortLabel: string, veryShortLabel?: string) => {
    const activeTabCount = [showSecurityAlerts, showRiskFilter, showWatchList, showAskAI].filter(Boolean).length;

    // Use very short labels when we have 5 tabs to ensure all fit
    if (activeTabCount === 5 && veryShortLabel) {
      return veryShortLabel;
    }

    // Use short labels when we have 4 tabs or on mobile devices
    if (activeTabCount === 4 || layout.isMobile) {
      return shortLabel;
    }

    // Use full labels when we have 3 or fewer tabs
    return fullLabel;
  };

  // Enhanced mobile bottom sheet pattern
  if (layout.isMobile) {
    return (
      <div
        ref={sidebarRef}
        className={`fixed bottom-0 left-0 right-0 z-40 transition-all duration-300 ease-in-out ${getResponsiveClasses()}`}
        style={{
          transform: isCollapsed ? 'translateY(100%)' : 'translateY(0)',
          boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.3)',
          backdropFilter: 'blur(16px)',
          background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%)',
          border: '1px solid rgba(148, 163, 184, 0.1)',
          borderBottom: 'none'
        }}
      >
        {/* Mobile header with close button */}
        <div className="flex items-center justify-between p-4 border-b border-border-accent">
          <h3 className="text-lg font-semibold text-foreground">Analysis Panel</h3>
          <button
            onClick={() => setIsCollapsed(true)}
            className="p-2 transition-colors rounded-lg hover:bg-background-tertiary"
          >
            <FaTimes className="text-foreground-muted" />
          </button>
        </div>

        {/* Mobile tab navigation with wrap support */}
        {showMultiplePanels && (
          <div
            className={`${getTabFlexClasses()} border-b border-border-accent`}
            style={{
              // Ensure minimum height and proper spacing for mobile tabs
              minHeight: '48px',
              // Better line height for mobile
              lineHeight: '1.2'
            }}
          >
            {showSecurityAlerts && (
              <button
                onClick={() => setActiveTab('alerts')}
                className={getTabButtonClasses(
                  activeTab === 'alerts',
                  activeTab === 'alerts'
                    ? 'text-red-400 border-b-2 border-red-400 bg-gradient-to-t from-red-500/20 to-red-500/5'
                    : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
                )}
              >
                <FaShieldAlt size={12} className="sm:w-[14px] sm:h-[14px] flex-shrink-0" />
                <span className="leading-tight text-center truncate">{getTabLabel('Alerts', 'Alert', 'Alrt')}</span>
              </button>
            )}

            {showRiskFilter && (
              <button
                onClick={() => setActiveTab('riskFilter')}
                className={getTabButtonClasses(
                  activeTab === 'riskFilter',
                  activeTab === 'riskFilter'
                    ? 'text-amber-400 border-b-2 border-amber-400 bg-gradient-to-t from-amber-500/20 to-amber-500/5'
                    : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
                )}
              >
                <FaFilter size={12} className="sm:w-[14px] sm:h-[14px] flex-shrink-0" />
                <span className="leading-tight text-center truncate">{getTabLabel('Filter', 'Risk', 'Risk')}</span>
              </button>
            )}
            {showWatchList && (
              <button
                onClick={() => setActiveTab('watchList')}
                className={getTabButtonClasses(
                  activeTab === 'watchList',
                  activeTab === 'watchList'
                    ? 'text-purple-400 border-b-2 border-purple-400 bg-gradient-to-t from-purple-500/20 to-purple-500/5'
                    : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
                )}
              >
                <FaEye size={12} className="sm:w-[14px] sm:h-[14px] flex-shrink-0" />
                <span className="leading-tight text-center truncate">{getTabLabel('Watch', 'List', 'List')}</span>
              </button>
            )}
            {showAskAI && (
              <button
                onClick={() => setActiveTab('askAI')}
                className={getTabButtonClasses(
                  activeTab === 'askAI',
                  activeTab === 'askAI'
                    ? 'text-emerald-400 border-b-2 border-emerald-400 bg-gradient-to-t from-emerald-500/20 to-emerald-500/5'
                    : 'text-gray-400 hover:text-white hover:bg-slate-700/50'
                )}
              >
                <FaRobot size={12} className="sm:w-[14px] sm:h-[14px] flex-shrink-0" />
                <span className="leading-tight text-center truncate">{getTabLabel('Ask AI', 'AI', 'AI')}</span>
              </button>
            )}
          </div>
        )}

        {/* Mobile content area with proper scroll support and positioning context */}
        <div className="relative flex-1 p-4 overflow-y-auto mobile-layout">
          {/* Security Alerts Panel */}
          {(showOnlyAlerts || (showMultiplePanels && activeTab === 'alerts')) && (
            <div className="h-full">
              <SecurityAlertSystem onAlertSelect={onAlertSelect} />
            </div>
          )}



          {/* Risk Filter Panel */}
          {(showOnlyRiskFilter || (showMultiplePanels && activeTab === 'riskFilter')) && (
            <div className="h-full">
              <RiskFilterPanel
                settings={riskFilterSettings}
                onSettingsChange={onRiskFilterChange || (() => {})}
                onApplyFilter={onApplyRiskFilter || (() => {})}
                onResetFilter={onResetRiskFilter || (() => {})}
                totalWallets={totalWallets}
                filteredWallets={filteredWallets}
                flaggedWallets={flaggedWallets}
              />
            </div>
          )}

          {/* Watch List Panel */}
          {(showOnlyWatchList || (showMultiplePanels && activeTab === 'watchList')) && (
            <div className="h-full">
              <ClientOnlyWatchListPanel
                onSelectWallet={onWalletSelect}
                onAddToWatchList={onAddToWatchList}
                onFocusWallet={onFocusWallet}
              />
            </div>
          )}

          {/* Ask AI Panel */}
          {(showOnlyAskAI || (showMultiplePanels && activeTab === 'askAI')) && (
            <div className="h-full">
              <ClientOnlyAskAIPanel
                selectedWallet={selectedWallet?.address}
              />
            </div>
          )}


        </div>
      </div>
    );
  }

  // Desktop and tablet layout
  return (
    <div
      ref={sidebarRef}
      className={`${position.position} z-40 transition-all duration-300 ease-in-out ${getResponsiveClasses()}`}
      style={{
        zIndex: position.zIndex,
        maxWidth: position.maxWidth,
        maxHeight: position.maxHeight,
        overflow: position.overflow,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(16px)',
        background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%)',
        border: '1px solid rgba(148, 163, 184, 0.1)',
        borderRadius: '0.75rem'
      }}
    >
      {/* Collapse/Expand Button */}
      {/* <div className="absolute z-30 -right-3 top-4">
        <button
          ref={setElementRef('collapseButton')}
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="flex items-center justify-center w-6 h-6 transition-colors border rounded-full shadow-lg bg-background-secondary border-border-accent hover:bg-background-tertiary"
          {...createTooltipHandlers('collapseButton')}
        >
          {isCollapsed ? <FaChevronRight size={10} /> : <FaChevronLeft size={10} />}
        </button>
      </div> */}

      {/* Collapsed State - Apply responsive flex pattern */}
      {isCollapsed && (
        <div className={`${getFlexClasses()} flex-col`}>
          {showSecurityAlerts && (
            <button
              ref={setElementRef('alertsCollapsed')}
              onClick={() => {
                setIsCollapsed(false);
                setActiveTab('alerts');
              }}
              className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 ${
                activeTab === 'alerts'
                  ? 'bg-gradient-to-r from-red-500/30 to-pink-500/20 text-red-400 border border-red-400/50 shadow-lg shadow-red-500/25'
                  : 'bg-slate-700/50 text-gray-400 border border-slate-600 hover:text-white hover:bg-slate-600/70 hover:border-slate-500'
              }`}
              {...createTooltipHandlers('alertsCollapsed')}
            >
              <FaShieldAlt size={16} />
            </button>
          )}



          {showRiskFilter && (
            <button
              ref={setElementRef('riskFilterCollapsed')}
              onClick={() => {
                setIsCollapsed(false);
                setActiveTab('riskFilter');
              }}
              className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 ${
                activeTab === 'riskFilter'
                  ? 'bg-gradient-to-r from-amber-500/30 to-yellow-500/20 text-amber-400 border border-amber-400/50 shadow-lg shadow-amber-500/25'
                  : 'bg-slate-700/50 text-gray-400 border border-slate-600 hover:text-white hover:bg-slate-600/70 hover:border-slate-500'
              }`}
              {...createTooltipHandlers('riskFilterCollapsed')}
            >
              <FaFilter size={16} />
            </button>
          )}

          {showWatchList && (
            <button
              ref={setElementRef('watchListCollapsed')}
              onClick={() => {
                setIsCollapsed(false);
                setActiveTab('watchList');
              }}
              className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 ${
                activeTab === 'watchList'
                  ? 'bg-gradient-to-r from-purple-500/30 to-indigo-500/20 text-purple-400 border border-purple-400/50 shadow-lg shadow-purple-500/25'
                  : 'bg-slate-700/50 text-gray-400 border border-slate-600 hover:text-white hover:bg-slate-600/70 hover:border-slate-500'
              }`}
              {...createTooltipHandlers('watchListCollapsed')}
            >
              <FaEye size={16} />
            </button>
          )}

          {showAskAI && (
            <button
              ref={setElementRef('askAICollapsed')}
              onClick={() => {
                setIsCollapsed(false);
                setActiveTab('askAI');
              }}
              className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 ${
                activeTab === 'askAI'
                  ? 'bg-gradient-to-r from-emerald-500/30 to-green-500/20 text-emerald-400 border border-emerald-400/50 shadow-lg shadow-emerald-500/25'
                  : 'bg-slate-700/50 text-gray-400 border border-slate-600 hover:text-white hover:bg-slate-600/70 hover:border-slate-500'
              }`}
              {...createTooltipHandlers('askAICollapsed')}
            >
              <FaRobot size={16} />
            </button>
          )}
        </div>
      )}

      {/* Expanded State */}
      {!isCollapsed && (
        <div
          className="flex flex-col border glass-card rounded-xl border-border-accent shadow-glow"
          style={{
            height: position.maxHeight || 'calc(100vh - 8rem)',
            minHeight: '300px',
            maxHeight: position.maxHeight || 'calc(100vh - 8rem)'
          }}
        >
          {/* Tab Navigation (only show if multiple panels are active) - Apply responsive flex pattern */}
          {showMultiplePanels && (
            <div
              className={`${getTabFlexClasses()} border-b border-border-accent bg-background-secondary/30`}
              style={{
                // Ensure minimum height and proper spacing for tabs
                minHeight: '44px',
                // Add custom CSS for better text wrapping
                lineHeight: '1.2'
              }}
            >
              {showSecurityAlerts && (
                <button
                  ref={setElementRef('alertsTab')}
                  onClick={() => setActiveTab('alerts')}
                  className={getTabButtonClasses(
                    activeTab === 'alerts',
                    activeTab === 'alerts'
                      ? 'text-red-400 border-b-2 border-red-400 bg-red-500/10'
                      : 'text-foreground-muted hover:text-foreground hover:bg-background-tertiary'
                  )}
                  {...createTooltipHandlers('alertsTab')}
                >
                  <FaShieldAlt size={12} className="lg:w-[14px] lg:h-[14px] flex-shrink-0" />
                  <span className="leading-tight text-center truncate">{getTabLabel('Alerts', 'Alert', 'Alrt')}</span>
                </button>
              )}



              {showRiskFilter && (
                <button
                  ref={setElementRef('riskFilterTab')}
                  onClick={() => setActiveTab('riskFilter')}
                  className={getTabButtonClasses(
                    activeTab === 'riskFilter',
                    activeTab === 'riskFilter'
                      ? 'text-yellow-400 border-b-2 border-yellow-400 bg-yellow-500/10'
                      : 'text-foreground-muted hover:text-foreground hover:bg-background-tertiary'
                  )}
                  {...createTooltipHandlers('riskFilterTab')}
                >
                  <FaFilter size={12} className="lg:w-[14px] lg:h-[14px] flex-shrink-0" />
                  <span className="leading-tight text-center truncate">{getTabLabel('Filter', 'Risk', 'Risk')}</span>
                </button>
              )}

              {showWatchList && (
                <button
                  ref={setElementRef('watchListTab')}
                  onClick={() => setActiveTab('watchList')}
                  className={getTabButtonClasses(
                    activeTab === 'watchList',
                    activeTab === 'watchList'
                      ? 'text-purple-400 border-b-2 border-purple-400 bg-purple-500/10'
                      : 'text-foreground-muted hover:text-foreground hover:bg-background-tertiary'
                  )}
                  {...createTooltipHandlers('watchListTab')}
                >
                  <FaEye size={12} className="lg:w-[14px] lg:h-[14px] flex-shrink-0" />
                  <span className="leading-tight text-center truncate">{getTabLabel('Watch', 'List', 'List')}</span>
                </button>
              )}

              {showAskAI && (
                <button
                  ref={setElementRef('askAITab')}
                  onClick={() => setActiveTab('askAI')}
                  className={getTabButtonClasses(
                    activeTab === 'askAI',
                    activeTab === 'askAI'
                      ? 'text-emerald-400 border-b-2 border-emerald-400 bg-emerald-500/10'
                      : 'text-foreground-muted hover:text-foreground hover:bg-background-tertiary'
                  )}
                  {...createTooltipHandlers('askAITab')}
                >
                  <FaRobot size={12} className="lg:w-[14px] lg:h-[14px] flex-shrink-0" />
                  <span className="leading-tight text-center truncate">{getTabLabel('Ask AI', 'AI', 'AI')}</span>
                </button>
              )}
            </div>
          )}

          {/* Panel Content with proper scroll support and positioning context */}
          <div className={`flex-1 min-h-0 overflow-hidden ${showMultiplePanels ? 'has-tabs' : ''}`}>
            {/* Security Alerts Panel */}
            {(showOnlyAlerts || (showMultiplePanels && activeTab === 'alerts')) && (
              <div className="h-full">
                <SecurityAlertSystem onAlertSelect={onAlertSelect} />
              </div>
            )}



            {/* Risk Filter Panel */}
            {(showOnlyRiskFilter || (showMultiplePanels && activeTab === 'riskFilter')) && (
              <div className="h-full">
                <RiskFilterPanel
                  settings={riskFilterSettings}
                  onSettingsChange={onRiskFilterChange || (() => {})}
                  onApplyFilter={onApplyRiskFilter || (() => {})}
                  onResetFilter={onResetRiskFilter || (() => {})}
                  totalWallets={totalWallets}
                  filteredWallets={filteredWallets}
                  flaggedWallets={flaggedWallets}
                />
              </div>
            )}

            {/* Watch List Panel */}
            {(showOnlyWatchList || (showMultiplePanels && activeTab === 'watchList')) && (
              <div className="h-full">
                <ClientOnlyWatchListPanel
                  onSelectWallet={onWalletSelect}
                  onAddToWatchList={onAddToWatchList}
                  onFocusWallet={onFocusWallet}
                />
              </div>
            )}

            {/* Ask AI Panel */}
            {(showOnlyAskAI || (showMultiplePanels && activeTab === 'askAI')) && (
              <div className="h-full">
                <ClientOnlyAskAIPanel
                  selectedWallet={selectedWallet?.address}
                />
              </div>
            )}


          </div>
        </div>
      )}

      {/* Enhanced Unified Tooltip Rendering with Scrolling Support and Center-Right Map Positioning */}
      {tooltip && typeof window !== 'undefined' && createPortal(
        <div
          className="fixed z-[99999] px-4 py-3 text-sm font-medium text-white bg-gray-900/90 backdrop-blur-xl rounded-xl shadow-glow border border-gray-700/40 transition-all duration-300 ease-out pointer-events-none max-w-sm break-words tooltip-center-right"
          style={{
            top: `${tooltip.position.top}px`,
            left: `${tooltip.position.left}px`,
            opacity: tooltip.isVisible ? 1 : 0,
            transform: 'translate(-50%, -50%)', // Center the tooltip at the calculated position
          }}
        >
          {/* Indicator arrow pointing to the sidebar (left side) */}
          <div className="absolute left-0 w-3 h-3 transform rotate-45 -translate-x-1/2 -translate-y-1/2 border-t border-l top-1/2 bg-gray-900/95 border-gray-700/50" />

          {/* Enhanced Scrollable Tooltip Content */}
          <div className="relative z-10 tooltip-container" style={{ maxHeight: 'min(300px, 70vh)' }}>
            <div className="font-medium leading-relaxed text-white tooltip-content vietnamese-text">
              {tooltip.content}
            </div>

            {/* Visual indicator for the tooltip source */}
            <div className="pt-2 mt-2 border-t border-gray-600/50">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-300">
                  {isVietnamese ? 'Từ thanh bên' : 'From sidebar'}
                </span>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default LeftSidebar;
