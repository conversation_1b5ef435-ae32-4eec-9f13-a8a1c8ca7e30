import { useState, useEffect, useRef, useCallback } from 'react';
import dynamic from 'next/dynamic';
import { FaSearch, FaNetworkWired, FaRocket, FaInfoCircle, FaLink, FaTimes, FaPause, FaShieldAlt, FaFilter, FaEye, FaRobot } from 'react-icons/fa';
import apiService from '@/services/apiService';
import { generateMockData, truncateAddress } from '@/utils/graphUtils';
import { GraphData, Node, Link } from '@/services/neo4jService';
import BubbleMap, { BubbleMapRefHandle } from './BubbleMap';
import BubbleMapControls from './BubbleMapControls';
import WalletAnalysisPanel from './WalletAnalysisPanel';
import ClientOnlyLeftSidebar from './ClientOnlyLeftSidebar';
import Tooltip from './Tooltip';
import HelpModal from './HelpModal';
import ClientOnlyPerformance from './ClientOnlyPerformance';
import { LayoutProvider, useLayout, ResponsiveContainer } from './LayoutManager';
import { useKeyboardShortcuts, createAppShortcuts, useHelpModal } from '@/hooks/useKeyboardShortcuts';
import { riskScoringService, RiskScore } from '@/services/riskScoringService';
// Performance manager will be imported dynamically on client-side

// Dynamically import the ForceGraph component to avoid SSR issues
const ForceGraph2D = dynamic(() => import('react-force-graph').then(mod => mod.ForceGraph2D), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-96">Loading graph visualization...</div>
});

interface BubbleMapContainerProps {
  searchWallet: string;
  onSelectWallet: (wallet: Node) => void;
  onSelectWalletPair?: (pair: {sourceWallet: Node, targetWallet: Node}) => void;
  onError?: (error: string | null) => void;
  onFocusWallet?: (address: string) => void; // Callback to focus on a specific wallet
}

// Fallback bubble visualization component
const SimpleBubbleMap = ({ graphData }: { graphData: GraphData }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !graphData.nodes.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    canvas.width = canvas.clientWidth;
    canvas.height = canvas.clientHeight;

    // Clear canvas completely - let the container's gradient background show through
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    // Don't fill with any color - keep it transparent to match container background

    // Center point
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // Draw links
    graphData.links.forEach(link => {
      const sourceNode = graphData.nodes.find(n => n.id === link.source) ||
                         graphData.nodes.find(n => n.id === (link.source as any)?.id);
      const targetNode = graphData.nodes.find(n => n.id === link.target) ||
                         graphData.nodes.find(n => n.id === (link.target as any)?.id);

      if (sourceNode && targetNode) {
        // Calculate positions based on node indices for a simple layout
        const sourceIndex = graphData.nodes.indexOf(sourceNode);
        const targetIndex = graphData.nodes.indexOf(targetNode);

        const sourceAngle = (sourceIndex / graphData.nodes.length) * Math.PI * 2;
        const targetAngle = (targetIndex / graphData.nodes.length) * Math.PI * 2;

        const radius = Math.min(canvas.width, canvas.height) * 0.4;

        const sourceX = centerX + Math.cos(sourceAngle) * radius;
        const sourceY = centerY + Math.sin(sourceAngle) * radius;
        const targetX = centerX + Math.cos(targetAngle) * radius;
        const targetY = centerY + Math.sin(targetAngle) * radius;

        // Draw link
        ctx.beginPath();
        ctx.moveTo(sourceX, sourceY);
        ctx.lineTo(targetX, targetY);
        ctx.strokeStyle = 'rgba(139, 92, 246, 0.3)';
        ctx.lineWidth = (link.value || 1) / 2;
        ctx.stroke();
      }
    });

    // Draw nodes
    graphData.nodes.forEach((node, index) => {
      // Calculate position based on index for a simple circular layout
      const angle = (index / graphData.nodes.length) * Math.PI * 2;
      const radius = Math.min(canvas.width, canvas.height) * 0.4;

      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;

      // Draw node
      ctx.beginPath();
      ctx.arc(x, y, node.size || 10, 0, Math.PI * 2);
      ctx.fillStyle = node.color || '#3B82F6';
      ctx.fill();
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Draw label if exists
      if (node.label) {
        ctx.fillStyle = 'white';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(node.label, x, y + (node.size || 10) + 15);
      }
    });
  }, [graphData]);

  return (
    <canvas
      ref={canvasRef}
      className="w-full h-full"
      style={{ touchAction: 'none' }}
    />
  );
};

// Internal component that uses layout context
const BubbleMapContainerInternal: React.FC<BubbleMapContainerProps> = ({ searchWallet, onSelectWallet, onSelectWalletPair, onError, onFocusWallet }) => {
  const { layout, updateLayout } = useLayout();
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [highlightedNode, setHighlightedNode] = useState<string | null>(null);
  const [searchDepth, setSearchDepth] = useState<number>(2);
  const [fullscreen, setFullscreen] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [forceGraphFailed, setForceGraphFailed] = useState<boolean>(false);
  const [showStats, setShowStats] = useState<boolean>(false);
  const [showLabels, setShowLabels] = useState<boolean>(true);

  // Cancellation state management
  const [isCancelled, setIsCancelled] = useState<boolean>(false);
  const [partialResults, setPartialResults] = useState<GraphData | null>(null);
  const [loadingStatus, setLoadingStatus] = useState<string>('');
  const [forceUpdate, setForceUpdate] = useState<number>(0);
  const [immediateCancel, setImmediateCancel] = useState<boolean>(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number>(1);
  const [tooltip, setTooltip] = useState<{content: string, x: number, y: number} | null>(null);

  // VietChain Talents 2025 Track 3 enhanced features with state persistence
  const [selectedWalletForAnalysis, setSelectedWalletForAnalysis] = useState<Node | null>(null);
  const [showWalletAnalysis, setShowWalletAnalysis] = useState<boolean>(false);
  const [selectedWalletPair, setSelectedWalletPair] = useState<{sourceWallet: Node, targetWallet: Node} | null>(null);


  const [showSecurityAlerts, setShowSecurityAlerts] = useState<boolean>(true);
  const [showRiskFilter, setShowRiskFilter] = useState<boolean>(false);
  const [showWatchList, setShowWatchList] = useState<boolean>(false);
  const [showAskAI, setShowAskAI] = useState<boolean>(false);
  const [forceActiveTab, setForceActiveTab] = useState<'alerts' | 'riskFilter' | 'watchList' | 'askAI' | null>(null);
  const [isHydrated, setIsHydrated] = useState(false);

  const [riskScores, setRiskScores] = useState<{ [address: string]: RiskScore }>({});
  const [alertedWallets, setAlertedWallets] = useState<Set<string>>(new Set());
  const [filteredNodes, setFilteredNodes] = useState<Node[]>([]);

  // Risk Filter Settings
  const [riskFilterSettings, setRiskFilterSettings] = useState({
    minRiskLevel: 'low' as 'low' | 'medium' | 'high' | 'critical',
    showOnlyFlagged: false,
    hideWhitelisted: true,
    highlightSuspicious: true,
    autoHideClean: false,
    riskThreshold: 50, // 0-100 scale
    filterTypes: {
      phishing: true,
      mev: true,
      laundering: true,
      sanctions: true,
      scam: true,
      suspicious: true
    }
  });

  // Performance optimization state (client-side only)
  const [showPerformanceConfig, setShowPerformanceConfig] = useState<boolean>(false);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState<boolean>(false); // Start false for SSR
  // Removed collapsed and position states as they're now handled internally by the dropdown
  const [isClient, setIsClient] = useState<boolean>(false);
  const [gpuOptimizer, setGpuOptimizer] = useState<any>(null);
  const [memoryManager, setMemoryManager] = useState<any>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);

  const containerRef = useRef<HTMLDivElement>(null);
  const bubbleMapRef = useRef<BubbleMapRefHandle>(null);

  // Help modal and keyboard shortcuts
  const { isHelpVisible, showHelp, hideHelp } = useHelpModal();

  // Create keyboard shortcuts
  const appShortcuts = createAppShortcuts({
    toggleSecurityAlerts: () => {
      setShowSecurityAlerts(!showSecurityAlerts);
      updateLayout({ showSecurityAlerts: !showSecurityAlerts });
    },

    toggleRiskFilter: () => {
      setShowRiskFilter(!showRiskFilter);
      updateLayout({ showRiskFilter: !showRiskFilter });
    },
    toggleWatchList: () => {
      setShowWatchList(!showWatchList);
      updateLayout({ showWatchList: !showWatchList });
    },
    toggleAskAI: () => {
      setShowAskAI(!showAskAI);
      updateLayout({ showAskAI: !showAskAI });
    },
    exportData: () => {
      console.log('Export data triggered');
    },
    toggleRealTime: () => {
      console.log('Toggle real-time monitoring');
    },
    reportWallet: () => {
      if (selectedWalletForAnalysis) {
        console.log('Report wallet:', selectedWalletForAnalysis.address);
      }
    },
    monitorWallet: () => {
      if (selectedWalletForAnalysis) {
        console.log('Monitor wallet:', selectedWalletForAnalysis.address);
      }
    },
    closePanel: () => {
      if (showWalletAnalysis) {
        setShowWalletAnalysis(false);
        updateLayout({ showWalletAnalysis: false });
      } else if (isHelpVisible) {
        hideHelp();
      }
    },
    showHelp: showHelp,

  });

  // Initialize keyboard shortcuts
  const { shortcuts } = useKeyboardShortcuts({
    shortcuts: appShortcuts,
    enabled: true
  });

  // Client-side initialization effect
  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      setIsClient(true);
      setShowPerformanceMonitor(true);

      // Load persisted state after hydration
      const savedSecurityAlerts = localStorage.getItem('bubbleMap_showSecurityAlerts');
      const savedRiskFilter = localStorage.getItem('bubbleMap_showRiskFilter');
      const savedWatchList = localStorage.getItem('bubbleMap_showWatchList');
      const savedAskAI = localStorage.getItem('bubbleMap_showAskAI');
      const savedRiskFilterSettings = localStorage.getItem('bubbleMap_riskFilterSettings');
      if (savedSecurityAlerts) {
        setShowSecurityAlerts(JSON.parse(savedSecurityAlerts));
      }
      if (savedRiskFilter) {
        setShowRiskFilter(JSON.parse(savedRiskFilter));
      }
      if (savedWatchList) {
        setShowWatchList(JSON.parse(savedWatchList));
      }
      if (savedAskAI) {
        setShowAskAI(JSON.parse(savedAskAI));
      }
      if (savedRiskFilterSettings) {
        setRiskFilterSettings(JSON.parse(savedRiskFilterSettings));
      }
      setIsHydrated(true);

      // Dynamically import and initialize performance managers
      import('@/utils/performanceManager').then(({ performanceManager, GPUOptimizer, MemoryManager }) => {
        const config = performanceManager.getConfig();
        setGpuOptimizer(new GPUOptimizer(config));
        setMemoryManager(new MemoryManager(config.dataCacheLimit));
        setPerformanceMetrics(performanceManager.getMetrics());
      }).catch(error => {
        console.warn('Failed to load performance manager:', error);
      });
    }
  }, []);



  useEffect(() => {
    if (isHydrated && typeof window !== 'undefined') {
      localStorage.setItem('bubbleMap_showSecurityAlerts', JSON.stringify(showSecurityAlerts));
    }
  }, [showSecurityAlerts, isHydrated]);

  useEffect(() => {
    if (isHydrated && typeof window !== 'undefined') {
      localStorage.setItem('bubbleMap_showRiskFilter', JSON.stringify(showRiskFilter));
    }
  }, [showRiskFilter, isHydrated]);

  useEffect(() => {
    if (isHydrated && typeof window !== 'undefined') {
      localStorage.setItem('bubbleMap_showWatchList', JSON.stringify(showWatchList));
    }
  }, [showWatchList, isHydrated]);

  useEffect(() => {
    if (isHydrated && typeof window !== 'undefined') {
      localStorage.setItem('bubbleMap_showAskAI', JSON.stringify(showAskAI));
    }
  }, [showAskAI, isHydrated]);

  // Reset forceActiveTab after it has been used
  useEffect(() => {
    if (forceActiveTab) {
      const timer = setTimeout(() => {
        setForceActiveTab(null);
      }, 100); // Reset after 100ms to allow LeftSidebar to process the change
      return () => clearTimeout(timer);
    }
  }, [forceActiveTab]);

  useEffect(() => {
    if (isHydrated && typeof window !== 'undefined') {
      localStorage.setItem('bubbleMap_riskFilterSettings', JSON.stringify(riskFilterSettings));
    }
  }, [riskFilterSettings, isHydrated]);

  // Calculate risk scores when graph data changes
  useEffect(() => {
    if (graphData.nodes.length === 0) return;

    const newRiskScores: { [address: string]: RiskScore } = {};
    const newAlertedWallets = new Set<string>();

    graphData.nodes.forEach(node => {
      const riskScore = riskScoringService.calculateRiskScore(node, graphData.links);
      newRiskScores[node.address] = riskScore;

      // Add to alerted wallets if high risk
      if (riskScore.totalScore >= 50 || riskScore.flags.length > 0) {
        newAlertedWallets.add(node.address);
      }
    });

    setRiskScores(newRiskScores);
    setAlertedWallets(newAlertedWallets);
  }, [graphData]);

  // Apply risk filter when settings change
  useEffect(() => {
    if (graphData.nodes.length === 0) return;

    const filtered = riskScoringService.filterNodesByRisk(graphData.nodes, riskFilterSettings);
    setFilteredNodes(filtered);
  }, [graphData.nodes, riskFilterSettings]);

  // Performance monitoring effect
  useEffect(() => {
    if (!isClient || !gpuOptimizer) return;

    const updatePerformanceMetrics = async () => {
      try {
        const { performanceManager } = await import('@/utils/performanceManager');
        const metrics = performanceManager.getMetrics();
        setPerformanceMetrics(metrics);

        // Update GPU optimizer with current camera and viewport
        if (containerRef.current && gpuOptimizer) {
          const rect = containerRef.current.getBoundingClientRect();
          gpuOptimizer.updateCamera(
            { x: 0, y: 0, zoom: currentZoom }, // Use actual camera position if available
            { width: rect.width, height: rect.height }
          );
        }

        // Update node count metric
        performanceManager.updateMetrics({ nodeCount: graphData.nodes.length });
      } catch (error) {
        console.warn('Failed to update performance metrics:', error);
      }
    };

    // Initial update
    updatePerformanceMetrics();

    // Set up interval for regular updates
    const interval = setInterval(updatePerformanceMetrics, 1000);

    return () => clearInterval(interval);
  }, [isClient, graphData.nodes.length, currentZoom, gpuOptimizer]);

  // Performance configuration change handler
  const handlePerformanceConfigChange = useCallback(async (newConfig: any) => {
    if (!isClient || !gpuOptimizer || !memoryManager) return;

    try {
      const { performanceManager } = await import('@/utils/performanceManager');

      gpuOptimizer.updateConfig(newConfig);
      memoryManager.setMaxSize(newConfig.dataCacheLimit);

      // Update real-time update intervals if needed
      if (newConfig.realTimeUpdateInterval !== performanceManager.getConfig().realTimeUpdateInterval) {
        // Restart any real-time update timers with new interval
        console.log('Updated real-time interval to:', newConfig.realTimeUpdateInterval);
      }
    } catch (error) {
      console.warn('Failed to update performance config:', error);
    }
  }, [isClient, gpuOptimizer, memoryManager]);

  // Performance monitor now uses fixed bottom positioning, no dynamic positioning needed

  // Cancel loading function with immediate state updates
  const handleCancelLoading = useCallback(() => {
    console.log('🔴 CANCEL BUTTON CLICKED - Starting cancellation process...');
    console.log('🔍 Current state before cancel:', {
      loading,
      isCancelled,
      hasAbortController: !!abortControllerRef.current,
      abortSignalAborted: abortControllerRef.current?.signal.aborted,
      partialResultsCount: partialResults?.nodes?.length || 0
    });

    // IMMEDIATE state updates to provide instant feedback
    console.log('⚡ Setting immediate state updates...');
    setImmediateCancel(true); // Immediate override
    setLoading(false);
    setIsCancelled(true);
    setForceUpdate(prev => prev + 1); // Force re-render

    // Cancel the ongoing request
    if (abortControllerRef.current && !abortControllerRef.current.signal.aborted) {
      console.log('🛑 Aborting current request...');
      abortControllerRef.current.abort();
      console.log('✅ AbortController.abort() called, signal aborted:', abortControllerRef.current.signal.aborted);
    } else {
      console.log('⚠️ No active AbortController found or already aborted');
    }

    // Show appropriate message based on partial results
    if (partialResults && partialResults.nodes && partialResults.nodes.length > 0) {
      console.log('📊 Showing partial results:', partialResults.nodes.length, 'nodes');
      setGraphData(partialResults);
      setLoadingStatus('Partial results shown - search was cancelled');
      onError?.('Partial results shown - search was cancelled');
    } else {
      console.log('❌ No partial results, showing cancellation message');
      setLoadingStatus('Search cancelled by user');
      onError?.('Search cancelled by user');
    }

    console.log('✅ Cancel operation completed - UI should update immediately');
  }, [loading, isCancelled, partialResults, onError]);

  // Reset cancellation state when starting new search
  const resetCancellationState = useCallback(() => {
    console.log('🔄 Resetting cancellation state for new search...');

    // Clear any existing timeouts to prevent state conflicts
    if (abortControllerRef.current) {
      console.log('🛑 Aborting previous request before starting new one');
      abortControllerRef.current.abort();
    }

    // Reset all cancellation-related state
    console.log('🧹 Clearing all cancellation-related state');
    setIsCancelled(false);
    setImmediateCancel(false);
    setPartialResults(null);
    setLoadingStatus('');
    setError(null);
    onError?.(null);

    // Create new abort controller for the new request
    abortControllerRef.current = new AbortController();
    console.log('🆕 Created new AbortController for new request');
  }, [onError]);

  // Load initial mock data with cancellation support
  useEffect(() => {
    let cancelled = false;

    const loadMockData = async () => {
      setLoading(true);
      setLoadingStatus('Initializing network visualization...');

      // Simulate loading time for better UX
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (cancelled) return;

      const mockData = generateMockData(100);
      console.log("Generated mock data:", mockData);

      // Center the graph in the container
      if (containerRef.current && !cancelled) {
        const rect = containerRef.current.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        // Adjust node positions to center in container
        mockData.nodes.forEach(node => {
          if (node.x !== undefined && node.y !== undefined) {
            node.x += centerX;
            node.y += centerY;
            node.targetX = node.x;
            node.targetY = node.y;
          }
        });
      }

      if (!cancelled) {
        setGraphData(mockData);
        setIsInitialized(true);
        setLoading(false);
        setLoadingStatus('');
      }
    };

    loadMockData();

    return () => {
      cancelled = true;
    };
  }, []);

  // Fetch data when search wallet changes
  useEffect(() => {
    if (!searchWallet) return;

    // Prevent double execution in React StrictMode
    let isActive = true;

    const fetchWalletNetwork = async () => {
      console.log('🚀 Starting fetchWalletNetwork for:', searchWallet);

      if (!isActive) {
        console.log('❌ Component not active, aborting fetch');
        return;
      }

      // Reset cancellation state and prepare for new search
      resetCancellationState();

      if (!isActive) {
        console.log('❌ Component not active after reset, aborting fetch');
        return;
      }

      console.log('⏳ Setting loading state to true');
      setLoading(true);
      setError(null);
      onError?.(null); // Clear global error
      setLoadingStatus('Analyzing wallet connections...');

      try {
        console.log('📡 Making API call with AbortController:', {
          hasAbortController: !!abortControllerRef.current,
          signalAborted: abortControllerRef.current?.signal.aborted
        });

        // Use the abort signal for cancellation support
        const data = await apiService.getWalletNetwork(
          searchWallet,
          searchDepth,
          abortControllerRef.current?.signal
        );

        console.log('📥 API call completed, received data:', {
          hasData: !!data,
          nodeCount: data?.nodes?.length || 0,
          linkCount: data?.links?.length || 0
        });

        // Check if request was cancelled or component unmounted
        if (!isActive || isCancelled || abortControllerRef.current?.signal.aborted) {
          console.log('Request was cancelled or component unmounted, not updating state');
          return;
        }

        // Validate data structure before accessing properties
        if (!isActive || !data || !data.nodes || !Array.isArray(data.nodes)) {
          if (!isActive) return;
          console.error('Invalid data structure received:', data);
          const errorMessage = 'Invalid data received from server. Please try again.';
          setError(errorMessage);
          onError?.(errorMessage);
          return;
        }

        if (data.nodes.length === 0) {
          const errorMessage = `No data found for wallet address: ${searchWallet}`;
          setError(errorMessage);
          onError?.(errorMessage);
          // Keep the previous graph data
        } else {
          // Center the graph in the container
          if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            // Initialize positions for nodes that don't have them
            data.nodes.forEach(node => {
              if (node.x === undefined || node.y === undefined) {
                const angle = Math.random() * Math.PI * 2;
                const radius = 150 + Math.random() * 50;
                node.x = centerX + Math.cos(angle) * radius;
                node.y = centerY + Math.sin(angle) * radius;
                node.targetX = node.x;
                node.targetY = node.y;
              }
            });
          }

          // Store partial results in case of cancellation
          setPartialResults(data);
          setGraphData(data);
          setLoadingStatus('Network analysis complete');

          // If we have the search wallet node, highlight it
          const searchNode = data.nodes.find(node => node.address === searchWallet);
          if (searchNode) {
            setHighlightedNode(searchNode.id);
            onSelectWallet(searchNode);
          }
        }
      } catch (err: any) {
        console.log('❌ Error in fetchWalletNetwork:', {
          errorMessage: err.message,
          errorType: err.constructor.name,
          isActive,
          isCancelled,
          signalAborted: abortControllerRef.current?.signal.aborted
        });

        // Handle cancellation gracefully - don't show errors for cancelled requests
        if (err.message === 'CANCELLED' || abortControllerRef.current?.signal.aborted) {
          console.log('🛑 Request was cancelled by user - not updating error state');
          // Don't update any state here - let the cancel handler manage it
          return;
        }

        // Only handle real errors if not cancelled and component is still active
        if (isActive && !isCancelled) {
          console.error('💥 Real error occurred:', err);
          const errorMessage = 'Failed to fetch wallet network data. Please try again.';
          setError(errorMessage);
          onError?.(errorMessage);
          setLoading(false);
          setLoadingStatus('');
        } else {
          console.log('🔇 Ignoring error due to cancellation or inactive component');
        }
      } finally {
        console.log('🏁 Finally block - checking if should update loading state:', {
          isActive,
          isCancelled,
          signalAborted: abortControllerRef.current?.signal.aborted,
          hasError: !!error
        });

        // Only update loading state if component is active and not cancelled
        if (isActive && !isCancelled && !abortControllerRef.current?.signal.aborted && !error) {
          console.log('✅ Clearing loading state in finally block');
          setLoading(false);
          setLoadingStatus('');
        } else {
          console.log('🔇 Not clearing loading state due to conditions');
        }
      }
    };

    fetchWalletNetwork();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isActive = false;
    };
  }, [searchWallet, searchDepth, onSelectWallet, resetCancellationState]);

  // Handle node click with enhanced analysis features and risk info
  const handleNodeClick = useCallback((node: Node) => {
    console.log('Node clicked:', node);
    setHighlightedNode(node.id);

    // Get risk score for the node
    const riskScore = riskScores[node.address];
    if (riskScore) {
      console.log('Risk score for', node.address, ':', riskScore);
    }

    // Ensure the node has all required properties before passing it to the parent component
    const completeNode = {
      ...node,
      // Add default values for any missing properties
      id: node.id || `node-${Math.random().toString(36).substr(2, 9)}`,
      address: node.address || 'Unknown',
      label: node.label || undefined,
      balance: node.balance || undefined,
      transactionCount: node.transactionCount || 0,
      tags: node.tags || [],
      riskScore: riskScore // Add risk score to node
    };

    // Set selected wallet for analysis panel
    setSelectedWalletForAnalysis(completeNode);
    setShowWalletAnalysis(true);
    // Clear wallet pair selection when selecting individual wallet
    setSelectedWalletPair(null);
    updateLayout({ showWalletAnalysis: true });

    // Pass the complete node object to the parent component
    onSelectWallet(completeNode);

    // For debugging - log the selected wallet
    console.log('Selected wallet:', completeNode);
  }, [onSelectWallet, updateLayout, riskScores]);

  // Handle link click to show pairwise transactions
  const handleLinkClick = useCallback((sourceNode: Node, targetNode: Node, link: any) => {
    console.log('Link clicked between:', sourceNode.address, 'and', targetNode.address);

    // Set the wallet pair for pairwise transaction analysis
    const walletPair = {
      sourceWallet: sourceNode,
      targetWallet: targetNode
    };

    setSelectedWalletPair(walletPair);

    // Also set one of the wallets as the primary selected wallet for the panel
    setSelectedWalletForAnalysis(sourceNode);
    setShowWalletAnalysis(true);
    updateLayout({ showWalletAnalysis: true });

    // Highlight both nodes
    setHighlightedNode(sourceNode.id);

    // Notify parent component about wallet pair selection
    onSelectWalletPair?.(walletPair);

    console.log('Selected wallet pair for pairwise analysis:', {
      source: sourceNode.address,
      target: targetNode.address
    });
  }, [updateLayout]);



  // Handle tooltip display
  const showTooltip = (content: string, x: number, y: number) => {
    setTooltip({ content, x, y });
  };

  const hideTooltip = () => {
    setTooltip(null);
  };

  // Node paint method to customize node appearance with risk indicators
  const paintNode = useCallback((node: any, ctx: CanvasRenderingContext2D) => {
    // Node radius
    const size = node.size || 5;

    // Get risk score for color coding
    const riskScore = riskScores[node.address];
    let nodeColor = node.color || '#3B82F6';

    // Apply risk-based coloring if risk filter is active and highlighting is enabled
    if (showRiskFilter && riskFilterSettings.highlightSuspicious && riskScore) {
      switch (riskScore.riskLevel) {
        case 'critical':
          nodeColor = '#DC2626'; // Red
          break;
        case 'high':
          nodeColor = '#EA580C'; // Orange
          break;
        case 'medium':
          nodeColor = '#D97706'; // Amber
          break;
        case 'low':
          nodeColor = '#059669'; // Green
          break;
        default:
          nodeColor = '#6B7280'; // Gray
      }
    }

    // Highlighted effect
    const isHighlighted = highlightedNode === node.id ||
                          highlightedNode === null ||
                          node.highlighted;

    // Draw outer circle (bubble)
    ctx.beginPath();
    ctx.arc(0, 0, size, 0, 2 * Math.PI);
    ctx.fillStyle = isHighlighted ? nodeColor : `${nodeColor}50`;
    ctx.fill();

    // Draw border with risk indication
    let borderColor = isHighlighted ? 'white' : `${nodeColor}80`;
    let borderWidth = isHighlighted ? 2 : 1;

    // Enhanced border for high-risk nodes
    if (riskScore && riskScore.totalScore >= 75) {
      borderColor = '#DC2626'; // Red border for critical risk
      borderWidth = 3;
    } else if (riskScore && riskScore.totalScore >= 50) {
      borderColor = '#EA580C'; // Orange border for high risk
      borderWidth = 2;
    }

    ctx.strokeStyle = borderColor;
    ctx.lineWidth = borderWidth;
    ctx.stroke();

    // Add risk indicator dot for flagged wallets
    if (riskScore && riskScore.flags.length > 0) {
      ctx.beginPath();
      ctx.arc(size * 0.7, -size * 0.7, size * 0.3, 0, 2 * Math.PI);
      ctx.fillStyle = '#DC2626';
      ctx.fill();
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 1;
      ctx.stroke();
    }

    // Add inner small circle for certain node types
    if (node.tags?.includes('Exchange') || node.tags?.includes('Contract')) {
      ctx.beginPath();
      ctx.arc(0, 0, size * 0.5, 0, 2 * Math.PI);
      ctx.fillStyle = isHighlighted ? 'white' : `${nodeColor}30`;
      ctx.fill();
    }

    // Add risk level text for high-risk nodes when zoomed in
    if (riskScore && riskScore.totalScore >= 50 && size > 8) {
      ctx.fillStyle = 'white';
      ctx.font = `${Math.max(8, size * 0.6)}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('!', 0, 0);
    }
  }, [highlightedNode, riskScores, showRiskFilter, riskFilterSettings.highlightSuspicious]);

  // Link paint method to customize link appearance
  const paintLink = useCallback((link: any, ctx: CanvasRenderingContext2D) => {
    // Set link properties
    ctx.strokeStyle = link.highlighted ? '#8B5CF6' : '#8B5CF650';
    ctx.lineWidth = link.value ? Math.min(Math.max(link.value / 2, 1), 5) : 1;

    // Draw link
    ctx.beginPath();
    ctx.moveTo(link.source.x || 0, link.source.y || 0);
    ctx.lineTo(link.target.x || 0, link.target.y || 0);
    ctx.stroke();
  }, []);

  // Add error handler for dynamic import
  useEffect(() => {
    // If ForceGraph fails to load after 5 seconds, use fallback
    const timer = setTimeout(() => {
      if (!bubbleMapRef.current && isInitialized) {
        console.warn("ForceGraph failed to load, using fallback visualization");
        setForceGraphFailed(true);
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [isInitialized]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cancel any ongoing requests when component unmounts
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Enhanced control handlers
  const handleZoomIn = () => {
    if (bubbleMapRef.current) {
      bubbleMapRef.current.zoomIn();
      setCurrentZoom(bubbleMapRef.current.getCurrentZoom());
    }
  };

  const handleZoomOut = () => {
    if (bubbleMapRef.current) {
      bubbleMapRef.current.zoomOut();
      setCurrentZoom(bubbleMapRef.current.getCurrentZoom());
    }
  };

  const handleResetView = () => {
    if (bubbleMapRef.current) {
      bubbleMapRef.current.resetView();
      setCurrentZoom(1);
    }
  };

  const toggleFullscreen = () => {
    if (fullscreen) {
      document.exitFullscreen().catch(err => console.error('Error exiting fullscreen:', err));
    } else if (containerRef.current) {
      containerRef.current.requestFullscreen().catch(err => console.error('Error entering fullscreen:', err));
    }
    setFullscreen(!fullscreen);
  };

  const toggleStats = () => {
    setShowStats(!showStats);
  };

  const toggleLabels = () => {
    setShowLabels(!showLabels);
  };

  // Handle focusing on a specific wallet from watch list
  const handleFocusWallet = useCallback((address: string) => {
    // Find the node in current graph data
    const targetNode = graphData.nodes.find(node =>
      node.address.toLowerCase() === address.toLowerCase()
    );

    if (targetNode && bubbleMapRef.current) {
      // Zoom to the node with smooth animation (2.5x zoom level)
      bubbleMapRef.current.zoomToNode(targetNode.id, 2.5);

      console.log('Zoomed to wallet:', address, targetNode);
    } else {
      console.log('Wallet not found in current graph:', address);
      // Optionally, you could trigger a search for this wallet
      if (onFocusWallet) {
        onFocusWallet(address);
      }
    }
  }, [graphData.nodes, onFocusWallet]);

  // Debug state monitoring
  useEffect(() => {
    console.log('🔍 State changed:', {
      loading,
      isCancelled,
      immediateCancel,
      hasAbortController: !!abortControllerRef.current,
      signalAborted: abortControllerRef.current?.signal.aborted,
      loadingStatus,
      partialResultsCount: partialResults?.nodes?.length || 0,
      graphDataCount: graphData.nodes.length,
      shouldShowLoading: loading && !immediateCancel
    });
  }, [loading, isCancelled, immediateCancel, loadingStatus, partialResults, graphData]);

  return (
    <div
      className="relative w-full h-full bg-gradient-to-br no-horizontal-scroll from-background via-background-secondary to-background-tertiary"
      ref={containerRef}
      style={{ minHeight: '600px' }}
    >
      {/* Enhanced Control Panel with Responsive Design */}
      <ResponsiveContainer component="controlPanel">
        <div className="control-panel-container">
          {/* Desktop/Tablet: Vertical layout */}
          <div className="flex-row hidden gap-3 md:flex">
            <Tooltip
              content="Toggle real-time security alerts and threat monitoring"
              contentVi="Bật/tắt cảnh báo bảo mật và giám sát mối đe dọa theo thời gian thực"
              showShortcut="Alt+S"
              position="center-right"
            >
              <button
                onClick={() => {
                  const newValue = !showSecurityAlerts;
                  setShowSecurityAlerts(newValue);
                  updateLayout({ showSecurityAlerts: newValue });
                  if (newValue) {
                    setForceActiveTab('alerts');
                  }
                }}
                className={`p-3 rounded-xl transition-all duration-200 backdrop-blur-sm shadow-lg hover:scale-105 active:scale-95 ${
                  showSecurityAlerts
                    ? 'text-red-400 border bg-red-500/20 border-red-500/30 shadow-red-500/20'
                    : 'border bg-background-secondary/80 text-foreground-muted border-border-accent hover:text-foreground hover:border-red-500/30'
                }`}
              >
                <FaShieldAlt size={16} />
              </button>
            </Tooltip>



            <Tooltip
              content="Apply risk-based filters to highlight suspicious wallets and transactions based on threat intelligence, behavioral analysis, and compliance rules"
              contentVi="Áp dụng bộ lọc dựa trên rủi ro để làm nổi bật các ví và giao dịch đáng ngờ dựa trên thông tin tình báo mối đe dọa, phân tích hành vi và quy tắc tuân thủ"
              showShortcut="Alt+F"
              position="center-right"
            >
              <button
                onClick={() => {
                  const newValue = !showRiskFilter;
                  setShowRiskFilter(newValue);
                  updateLayout({ showRiskFilter: newValue });
                  if (newValue) {
                    setForceActiveTab('riskFilter');
                  }
                }}
                className={`p-3 rounded-xl transition-all duration-200 backdrop-blur-sm shadow-lg hover:scale-105 active:scale-95 ${
                  showRiskFilter
                    ? 'text-yellow-400 border bg-yellow-500/20 border-yellow-500/30 shadow-yellow-500/20'
                    : 'border bg-background-secondary/80 text-foreground-muted border-border-accent hover:text-foreground hover:border-yellow-500/30'
                }`}
              >
                <FaFilter size={16} />
              </button>
            </Tooltip>

            <Tooltip
              content="Toggle watch list for monitoring wallets"
              contentVi="Bật/tắt danh sách theo dõi ví"
              showShortcut="Alt+W"
              position="center-right"
            >
              <button
                onClick={() => {
                  const newValue = !showWatchList;
                  setShowWatchList(newValue);
                  updateLayout({ showWatchList: newValue });
                  if (newValue) {
                    setForceActiveTab('watchList');
                  }
                }}
                className={`p-3 transition-all duration-200 border shadow-lg rounded-xl backdrop-blur-sm hover:scale-105 active:scale-95 ${
                  showWatchList
                    ? 'text-purple-400 border bg-purple-500/20 border-purple-500/30 shadow-purple-500/20'
                    : 'border bg-background-secondary/80 text-foreground-muted border-border-accent hover:text-foreground hover:border-purple-500/30'
                }`}
              >
                <FaEye size={16} />
              </button>
            </Tooltip>

            <Tooltip
              content="Ask AI assistant for blockchain analysis and insights"
              contentVi="Hỏi trợ lý AI để phân tích blockchain và thông tin chi tiết"
              showShortcut="Alt+A"
              position="center-right"
            >
              <button
                onClick={() => {
                  const newValue = !showAskAI;
                  setShowAskAI(newValue);
                  updateLayout({ showAskAI: newValue });
                  if (newValue) {
                    setForceActiveTab('askAI');
                  }
                }}
                className={`p-3 transition-all duration-200 border shadow-lg rounded-xl backdrop-blur-sm hover:scale-105 active:scale-95 ${
                  showAskAI
                    ? 'text-emerald-400 border bg-emerald-500/20 border-emerald-500/30 shadow-emerald-500/20'
                    : 'border bg-background-secondary/80 text-foreground-muted border-border-accent hover:text-foreground hover:border-emerald-500/30'
                }`}
              >
                <FaRobot size={16} />
              </button>
            </Tooltip>

            <Tooltip
              content="Show keyboard shortcuts and help guide"
              contentVi="Hiển thị phím tắt và hướng dẫn trợ giúp"
              showShortcut="Ctrl+H or ?"
              position="center-right"
            >
              <button
                onClick={showHelp}
                className="p-3 transition-all duration-200 border shadow-lg rounded-xl backdrop-blur-sm bg-background-secondary/80 text-foreground-muted border-border-accent hover:text-foreground hover:border-blue-500/30 hover:scale-105 active:scale-95"
              >
                <FaInfoCircle size={16} />
              </button>
            </Tooltip>
          </div>

          {/* Mobile: Horizontal FAB layout */}
          <div className="flex gap-2 p-3 rounded-full md:hidden glass-card shadow-glow">
            <button
              onClick={() => {
                const newValue = !showSecurityAlerts;
                setShowSecurityAlerts(newValue);
                updateLayout({ showSecurityAlerts: newValue });
                if (newValue) {
                  setForceActiveTab('alerts');
                }
              }}
              className={`p-3 rounded-full transition-all duration-200 backdrop-blur-sm shadow-lg active:scale-95 ${
                showSecurityAlerts
                  ? 'text-red-400 border bg-red-500/20 border-red-500/30 shadow-red-500/20'
                  : 'border bg-background-secondary/80 text-foreground-muted border-border-accent'
              }`}
              aria-label="Toggle Security Alerts"
            >
              <FaShieldAlt size={14} />
            </button>



            <button
              onClick={() => {
                const newValue = !showRiskFilter;
                setShowRiskFilter(newValue);
                updateLayout({ showRiskFilter: newValue });
                if (newValue) {
                  setForceActiveTab('riskFilter');
                }
              }}
              className={`p-3 rounded-full transition-all duration-200 backdrop-blur-sm shadow-lg active:scale-95 ${
                showRiskFilter
                  ? 'text-yellow-400 border bg-yellow-500/20 border-yellow-500/30 shadow-yellow-500/20'
                  : 'border bg-background-secondary/80 text-foreground-muted border-border-accent'
              }`}
              aria-label="Toggle Risk Filter"
            >
              <FaFilter size={14} />
            </button>

            <button
              onClick={() => {
                const newValue = !showWatchList;
                setShowWatchList(newValue);
                updateLayout({ showWatchList: newValue });
                if (newValue) {
                  setForceActiveTab('watchList');
                }
              }}
              className={`p-3 rounded-full transition-all duration-200 backdrop-blur-sm shadow-lg active:scale-95 ${
                showWatchList
                  ? 'text-purple-400 border bg-purple-500/20 border-purple-500/30 shadow-purple-500/20'
                  : 'border bg-background-secondary/80 text-foreground-muted border-border-accent'
              }`}
              aria-label="Toggle Watch List"
            >
              <FaEye size={14} />
            </button>

            <button
              onClick={() => {
                const newValue = !showAskAI;
                setShowAskAI(newValue);
                updateLayout({ showAskAI: newValue });
                if (newValue) {
                  setForceActiveTab('askAI');
                }
              }}
              className={`p-3 rounded-full transition-all duration-200 backdrop-blur-sm shadow-lg active:scale-95 ${
                showAskAI
                  ? 'text-emerald-400 border bg-emerald-500/20 border-emerald-500/30 shadow-emerald-500/20'
                  : 'border bg-background-secondary/80 text-foreground-muted border-border-accent'
              }`}
              aria-label="Toggle Ask AI"
            >
              <FaRobot size={14} />
            </button>

            <button
              onClick={showHelp}
              className="p-3 transition-all duration-200 border rounded-full shadow-lg backdrop-blur-sm bg-background-secondary/80 text-foreground-muted border-border-accent active:scale-95"
              aria-label="Show Help"
            >
              <FaInfoCircle size={14} />
            </button>
          </div>
        </div>
      </ResponsiveContainer>

      {/* Enhanced loading state with cancel button - respect immediate cancel */}
      {loading && !immediateCancel && (
        <div className="absolute inset-0 z-20 flex items-center justify-center glass-card">
          <div className="flex flex-col items-center max-w-md mx-4">
            <div className="relative mb-6">
              <div className="absolute inset-0 rounded-full opacity-30 blur-lg animate-pulse bg-gradient-brand"></div>
              <div className="relative flex items-center justify-center w-16 h-16 rounded-full animate-spin bg-gradient-brand">
                <FaNetworkWired className="text-xl text-white" />
              </div>
            </div>
            <h3 className="mb-2 text-xl font-bold gradient-text">Loading Network</h3>
            <p className="mb-4 text-center text-foreground-muted">
              {loadingStatus || 'Analyzing wallet connections...'}
            </p>

            {/* Cancel Button with enhanced event handling and immediate feedback */}
            <button
              onClick={(e) => {
                console.log('🖱️ Cancel button clicked - event triggered');
                console.log('🖱️ Event details:', {
                  type: e.type,
                  target: e.target,
                  currentTarget: e.currentTarget,
                  timeStamp: e.timeStamp
                });
                e.preventDefault();
                e.stopPropagation();

                // Immediate visual feedback
                e.currentTarget.style.transform = 'scale(0.95)';
                e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.4)';

                // Call the handler
                handleCancelLoading();

                // Reset visual feedback after a short delay
                setTimeout(() => {
                  if (e.currentTarget) {
                    e.currentTarget.style.transform = '';
                    e.currentTarget.style.backgroundColor = '';
                  }
                }, 150);
              }}
              onMouseDown={(e) => {
                console.log('🖱️ Cancel button mouse down');
                e.preventDefault();
                e.stopPropagation();
              }}
              onTouchStart={(e) => {
                console.log('📱 Cancel button touch start');
                e.preventDefault();
                e.stopPropagation();
              }}
              className="flex items-center gap-2 px-6 py-3 font-semibold text-red-400 transition-all duration-200 border shadow-lg cursor-pointer select-none rounded-xl backdrop-blur-sm bg-red-500/20 hover:bg-red-500/30 active:bg-red-500/40 border-red-500/40 hover:text-red-300 active:text-red-200 hover:shadow-red-500/20"
              aria-label="Cancel loading"
              type="button"
              disabled={false}
              style={{
                WebkitTapHighlightColor: 'transparent',
                userSelect: 'none',
                touchAction: 'manipulation'
              }}
            >
              <FaTimes className="text-sm" />
              <span className="font-medium">Cancel Search</span>
            </button>

            {/* Progress indicator */}
            <div className="w-full max-w-xs mt-4">
              <div className="h-1 overflow-hidden rounded-full bg-background-tertiary">
                <div className="h-full rounded-full animate-pulse bg-gradient-brand"></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status message for cancelled/partial results */}
      {loadingStatus && !loading && (
        <div className="absolute z-30 max-w-md mx-4 transform -translate-x-1/2 top-4 left-1/2">
          <div className="px-4 py-3 border rounded-lg glass-card shadow-glow border-yellow-500/30 animate-slide-down">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-yellow-400">{loadingStatus}</span>
              <button
                onClick={() => setLoadingStatus('')}
                className="ml-auto text-yellow-400 transition-colors hover:text-yellow-300"
                aria-label="Dismiss status"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Debug Panel - Client-side only to prevent hydration errors */}
      {/* {process.env.NODE_ENV === 'development' && isClient && (
        <div className="absolute z-50 max-w-xs p-3 font-mono text-xs text-white rounded-lg top-4 right-4 bg-black/80">
          <div className="mb-2 font-bold">Debug State:</div>
          <div>Loading: {loading ? '✅' : '❌'}</div>
          <div>Immediate Cancel: {immediateCancel ? '✅' : '❌'}</div>
          <div>Cancelled: {isCancelled ? '✅' : '❌'}</div>
          <div>Show Loading: {(loading && !immediateCancel) ? '✅' : '❌'}</div>
          <div>AbortController: {abortControllerRef.current ? '✅' : '❌'}</div>
          <div>Signal Aborted: {abortControllerRef.current?.signal.aborted ? '✅' : '❌'}</div>
          <div>Status: {loadingStatus || 'None'}</div>
          <div>Partial Results: {partialResults?.nodes?.length || 0}</div>
          <div>Graph Data: {graphData.nodes.length}</div>
          <div>Force Update: {forceUpdate}</div>
          <div className="pt-2 mt-2 border-t border-gray-600">
            <div className="font-bold">Tooltip Info:</div>
            <div>Viewport: {window.innerWidth}x{window.innerHeight}</div>
            <div>TopBar Buffer: 120px</div>
          </div>
        </div>
      )} */}

      {/* Error state is now handled globally at the top level */}

      {/* Enhanced Controls using new BubbleMapControls component */}
      <BubbleMapControls
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetView={handleResetView}
        onToggleFullscreen={toggleFullscreen}
        isFullscreen={fullscreen}
        currentZoom={currentZoom}
        nodeCount={graphData.nodes.length}
        linkCount={graphData.links.length}
        showStats={showStats}
        onToggleStats={toggleStats}
        showLabels={showLabels}
        onToggleLabels={toggleLabels}
        imageNodeCount={graphData.nodes.filter(node => node.imageUrl || node.avatar).length}
        colorNodeCount={graphData.nodes.filter(node => !(node.imageUrl || node.avatar)).length}
        showLegend={true}
      />



      {/* Mobile-friendly interaction tips */}
      <div className="absolute z-10 right-3 bottom-3">
        <div className="relative group">
          <button className="flex items-center justify-center p-2 transition-colors border rounded-full shadow-lg backdrop-blur-sm bg-background/80 text-primary hover:text-white hover:bg-primary border-accent/20">
            <FaInfoCircle size={16} />
          </button>
          <div className="absolute right-0 hidden w-48 p-3 mb-2 border rounded-lg shadow-lg bottom-full backdrop-blur-sm group-hover:block bg-background/95 border-accent/30">
            <h3 className="mb-1 text-sm font-medium">Interaction Tips:</h3>
            <ul className="space-y-1 text-xs text-foreground/70">
              <li className="flex items-center gap-1"><span className="p-1 rounded-full bg-accent/20"><FaLink size={8} /></span> Drag nodes to move them</li>
              <li className="flex items-center gap-1"><span className="p-1 rounded-full bg-accent/20"><FaLink size={8} /></span> Click to view details</li>
              <li className="flex items-center gap-1"><span className="p-1 rounded-full bg-accent/20"><FaLink size={8} /></span> Drag empty space to pan</li>
              <li className="flex items-center gap-1"><span className="p-1 rounded-full bg-accent/20"><FaLink size={8} /></span> Scroll to zoom in/out</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Node tooltip with enhanced styling */}
      {tooltip && (
        <div
          className="absolute z-[99999] px-3 py-2 text-sm font-medium text-white bg-gray-900/90 backdrop-blur-xl rounded-xl shadow-glow border border-gray-700/40 transition-all duration-300 ease-out pointer-events-none max-w-xs break-words"
          style={{
            left: `${tooltip.x}px`,
            top: `${tooltip.y}px`,
            transform: 'translate(-50%, -100%)',
            marginTop: '-12px'
          }}
        >
          {tooltip.content}
        </div>
      )}

      {isInitialized && graphData.nodes.length > 0 && (
        <BubbleMap
          graphData={graphData}
          onNodeClick={handleNodeClick}
          onLinkClick={handleLinkClick}
          showRiskIndicators={true}

          riskScores={Object.fromEntries(
            Object.entries(riskScores).map(([address, riskScore]) => [address, riskScore.totalScore])
          )}
          alertedWallets={alertedWallets}
          ref={bubbleMapRef}
        />
      )}

      {/* Enhanced Features with Improved Layout */}

      {/* Left Sidebar - Combines Transaction Flow, Security Alerts, and Risk Filter */}
      <ClientOnlyLeftSidebar
        selectedWallet={selectedWalletForAnalysis}
        showSecurityAlerts={showSecurityAlerts}
        showRiskFilter={showRiskFilter}
        showWatchList={showWatchList}
        showAskAI={showAskAI}
        riskFilterSettings={riskFilterSettings}
        forceActiveTab={forceActiveTab}
        totalWallets={graphData.nodes.length}
        filteredWallets={filteredNodes.length}
        flaggedWallets={alertedWallets.size}
        onAlertSelect={(alert: any) => {
          console.log('Alert selected:', alert);
          // Find and highlight the alerted wallet
          const alertedNode = graphData.nodes.find(node =>
            node.address === alert.walletAddress
          );
          if (alertedNode) {
            setSelectedWalletForAnalysis(alertedNode);
            setShowWalletAnalysis(true);
            setHighlightedNode(alertedNode.id);
            updateLayout({ showWalletAnalysis: true });
          }
        }}

        onRiskFilterChange={(settings: typeof riskFilterSettings) => {
          setRiskFilterSettings(settings);
        }}
        onApplyRiskFilter={(settings: typeof riskFilterSettings) => {
          console.log('Applying risk filter:', settings);
          setRiskFilterSettings(settings);

          // Apply filter immediately
          const filtered = riskScoringService.filterNodesByRisk(graphData.nodes, settings);
          setFilteredNodes(filtered);

          // Update bubble map to show only filtered nodes
          if (bubbleMapRef.current) {
            const filteredGraphData = {
              nodes: filtered,
              links: graphData.links.filter(link =>
                filtered.some(node => node.id === link.source) &&
                filtered.some(node => node.id === link.target)
              )
            };
            // TODO: Update bubble map with filtered data
            console.log('Filtered graph data:', filteredGraphData);
          }
        }}
        onResetRiskFilter={() => {
          console.log('Resetting risk filter');
          // Reset to default settings
          setRiskFilterSettings({
            minRiskLevel: 'low',
            showOnlyFlagged: false,
            hideWhitelisted: true,
            highlightSuspicious: true,
            autoHideClean: false,
            riskThreshold: 50,
            filterTypes: {
              phishing: true,
              mev: true,
              laundering: true,
              sanctions: true,
              scam: true,
              suspicious: true
            }
          });
        }}
        onWalletSelect={(wallet) => {
          setSelectedWalletForAnalysis(wallet);
          setShowWalletAnalysis(true);
          updateLayout({ showWalletAnalysis: true });
        }}
        onAddToWatchList={(address) => {
          console.log('Added to watch list:', address);
        }}
        onFocusWallet={handleFocusWallet}
      />



      {/* Wallet Analysis Panel - Now handled by RightSidePanel in index.tsx */}

      {(!isInitialized || (graphData.nodes.length === 0 && !loading && !error)) && (
        <div className="flex flex-col items-center justify-center h-full p-8">
          {!isInitialized ? (
            <>
              <div className="relative mb-8">
                <div className="absolute inset-0 rounded-full opacity-30 blur-lg animate-pulse bg-gradient-brand"></div>
                <div className="relative flex items-center justify-center w-16 h-16 rounded-full animate-spin bg-gradient-brand">
                  <FaNetworkWired className="text-xl text-white" />
                </div>
              </div>
              <h3 className="mb-3 text-2xl font-bold gradient-text">Initializing Network</h3>
              <p className="text-foreground-muted">Setting up the visualization engine...</p>
            </>
          ) : (
            <>
              <div className="relative mb-8">
                <div className="absolute inset-0 rounded-full opacity-20 blur-xl bg-gradient-brand"></div>
                <div className="relative flex items-center justify-center w-24 h-24 border rounded-full bg-gradient-to-br from-background-secondary to-background-tertiary border-border-accent">
                  <FaRocket className="text-4xl text-accent-400" />
                </div>
              </div>
              <h3 className="mb-4 text-2xl font-bold gradient-text">Ready to Explore</h3>
              <p className="max-w-lg mb-6 leading-relaxed text-center text-foreground-muted">
                Enter a wallet address, ENS name, or token contract in the search bar above to visualize
                its transaction network and discover connected addresses.
              </p>
              <div className="flex items-center gap-4 text-sm text-foreground-muted">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-bubble-blue"></div>
                  <span>Wallets</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-bubble-green"></div>
                  <span>Exchanges</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-bubble-purple"></div>
                  <span>Contracts</span>
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {/* Help Modal */}
      <HelpModal
        isVisible={isHelpVisible}
        onClose={hideHelp}
        shortcuts={shortcuts}
      />

      {/* Performance Components - Client-side only */}
      <ClientOnlyPerformance
        showPerformanceMonitor={showPerformanceMonitor}
        showPerformanceConfig={showPerformanceConfig}
        onOpenSettings={() => setShowPerformanceConfig(true)}
        onCloseSettings={() => setShowPerformanceConfig(false)}
        onConfigChange={handlePerformanceConfigChange}
      />


    </div>
  );
};

// Main wrapper component with layout provider
const BubbleMapContainer: React.FC<BubbleMapContainerProps> = (props) => {
  return (
    <LayoutProvider>
      <BubbleMapContainerInternal {...props} />
    </LayoutProvider>
  );
};

export default BubbleMapContainer;
