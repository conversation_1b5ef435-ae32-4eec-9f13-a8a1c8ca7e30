import { useEffect, useCallback } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  action: () => void;
  description: string;
  descriptionVi?: string;
}

interface UseKeyboardShortcutsProps {
  shortcuts: KeyboardShortcut[];
  enabled?: boolean;
}

export const useKeyboardShortcuts = ({ shortcuts, enabled = true }: UseKeyboardShortcutsProps) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Don't trigger shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
      return;
    }

    const matchingShortcut = shortcuts.find(shortcut => {
      const keyMatch = shortcut.key.toLowerCase() === event.key.toLowerCase();
      const ctrlMatch = !!shortcut.ctrlKey === event.ctrlKey;
      const altMatch = !!shortcut.altKey === event.altKey;
      const shiftMatch = !!shortcut.shiftKey === event.shiftKey;

      return keyMatch && ctrlMatch && altMatch && shiftMatch;
    });

    if (matchingShortcut) {
      event.preventDefault();
      event.stopPropagation();
      matchingShortcut.action();
    }
  }, [shortcuts, enabled]);

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [handleKeyDown, enabled]);

  // Helper function to format shortcut display
  const formatShortcut = (shortcut: KeyboardShortcut): string => {
    const parts: string[] = [];
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    parts.push(shortcut.key.toUpperCase());
    return parts.join('+');
  };

  return {
    formatShortcut,
    shortcuts: shortcuts.map(shortcut => ({
      ...shortcut,
      formatted: formatShortcut(shortcut)
    }))
  };
};

// Predefined shortcuts for the application
export const createAppShortcuts = (actions: {
  toggleSecurityAlerts: () => void;
  toggleRiskFilter: () => void;
  toggleWatchList: () => void;
  toggleAskAI: () => void;
  exportData: () => void;
  toggleRealTime: () => void;
  reportWallet: () => void;
  monitorWallet: () => void;
  closePanel: () => void;
  showHelp: () => void;
}) => [
  {
    key: 's',
    altKey: true,
    action: actions.toggleSecurityAlerts,
    description: 'Toggle Security Alerts',
    descriptionVi: 'Bật/tắt Cảnh báo Bảo mật'
  },

  {
    key: 'f',
    altKey: true,
    action: actions.toggleRiskFilter,
    description: 'Toggle Risk Filter',
    descriptionVi: 'Bật/tắt Bộ lọc Rủi ro'
  },
  {
    key: 'w',
    altKey: true,
    action: actions.toggleWatchList,
    description: 'Toggle Watch List',
    descriptionVi: 'Bật/tắt Danh sách Theo dõi'
  },
  {
    key: 'a',
    altKey: true,
    action: actions.toggleAskAI,
    description: 'Toggle Ask AI Assistant',
    descriptionVi: 'Bật/tắt Trợ lý AI'
  },
  {
    key: 'e',
    ctrlKey: true,
    action: actions.exportData,
    description: 'Export Data',
    descriptionVi: 'Xuất Dữ liệu'
  },
  {
    key: 'e',
    ctrlKey: true,
    shiftKey: true,
    action: actions.exportData,
    description: 'Export Security Report',
    descriptionVi: 'Xuất Báo cáo Bảo mật'
  },
  {
    key: ' ',
    action: actions.toggleRealTime,
    description: 'Toggle Real-time Monitoring',
    descriptionVi: 'Bật/tắt Giám sát Thời gian thực'
  },
  {
    key: 'r',
    ctrlKey: true,
    action: actions.reportWallet,
    description: 'Report Selected Wallet',
    descriptionVi: 'Báo cáo Ví được chọn'
  },
  {
    key: 'm',
    ctrlKey: true,
    action: actions.monitorWallet,
    description: 'Monitor Selected Wallet',
    descriptionVi: 'Theo dõi Ví được chọn'
  },
  {
    key: 'Escape',
    action: actions.closePanel,
    description: 'Close Active Panel',
    descriptionVi: 'Đóng Panel đang hoạt động'
  },
  {
    key: 'h',
    ctrlKey: true,
    action: actions.showHelp,
    description: 'Show Keyboard Shortcuts Help',
    descriptionVi: 'Hiển thị Trợ giúp Phím tắt'
  },
  {
    key: '?',
    action: actions.showHelp,
    description: 'Show Help',
    descriptionVi: 'Hiển thị Trợ giúp'
  }
];

// Hook for managing help modal
export const useHelpModal = () => {
  const [isHelpVisible, setIsHelpVisible] = useState(false);

  const showHelp = useCallback(() => setIsHelpVisible(true), []);
  const hideHelp = useCallback(() => setIsHelpVisible(false), []);

  return {
    isHelpVisible,
    showHelp,
    hideHelp
  };
};

// Import useState for the help modal hook
import { useState } from 'react';
